version: 0.1
applications:
  - appRoot: .
    frontend:
      phases:
        preBuild:
          commands:
            - npm ci
        build:
          commands:
            - npx expo export --platform web
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    environmentVariables:
      - name: PROXY_URL
        value: https://casapay-ments.test
    buildSpec: amplify.yml
