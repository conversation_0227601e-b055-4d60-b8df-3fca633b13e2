import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { getApiBaseUrl } from './envUtils';
import type { PaymentAgreement } from '@/types'; // Added import

/**
 * Handles API responses and checks for authentication errors
 * @param response The API response JSON
 * @param logout Optional logout function to call
 * @returns The original response if no auth errors
 * @throws Error if authentication is required
 */
export const handleApiResponse = (response: any, logout?: () => Promise<void>) => {
  // Check for authentication errors
  if (
    // Check for AUTHENTICATION_REQUIRED error code
    (response.error && 
      (response.error.code === 'AUTHENTICATION_REQUIRED' || 
       response.error.message === 'Not authenticated')) ||
    // Check for "The provided credentials are incorrect" message
    (response.message === 'The provided credentials are incorrect.') ||
    // Check for 401 status code
    (response.status === 401)
  ) {
    console.log('Authentication error, redirecting to login');
    
    // Clear auth data
    AsyncStorage.removeItem('authToken');
    AsyncStorage.removeItem('user');
    
    // Call logout function if provided
    if (logout) {
      logout();
    } else {
      // Otherwise just redirect to login
      router.replace('/(auth)/login');
    }
    
    // If we have specific error messages, throw those
    if (response.message) {
      throw new Error(response.message);
    } else if (response.error?.message) {
      throw new Error(response.error.message);
    } else {
      throw new Error('Authentication required. Please log in again.');
    }
  }
  
  return response;
};

/**
 * Get the full API URL with the proxy base URL
 * @param path API endpoint path or full URL
 * @returns Full API URL with proxy base URL if needed
 * @throws Error if PROXY_URL environment variable is not set
 */
export const getFullApiUrl = (path: string): string => {
  // Get the API base URL - this will throw an error if PROXY_URL is not set
  const baseUrl = getApiBaseUrl();
  
  // If the path is already a full URL, check if it needs to be replaced
  if (path.startsWith('http://') || path.startsWith('https://')) {
    // Replace any hardcoded domains with the proxy URL
    if (path.includes('casapay-ments.test')) {
      return path.replace('https://casapay-ments.test', baseUrl);
    }
    return path;
  }
  
  // Otherwise, prepend the API base URL
  return `${baseUrl}${path.startsWith('/') ? '' : '/'}${path}`;
};

/**
 * Makes an authenticated API request and handles auth errors
 * @param url API endpoint URL or path
 * @param method HTTP method
 * @param body Request body
 * @param logout Optional logout function
 * @returns API response
 */
export const apiRequest = async (
  url: string, 
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST',
  body?: any,
  logout?: () => Promise<void>
) => {
  try {
    // Get auth token and user
    const token = await AsyncStorage.getItem('authToken');
    const userJson = await AsyncStorage.getItem('user');
    let currentUser = null;
    if (userJson) {
      currentUser = JSON.parse(userJson);
    }

    if (currentUser && currentUser.email === '<EMAIL>' && token === 'mock-auth-token-for-demo-user') {
      const fullUrlPath = getFullApiUrl(url);

      if (fullUrlPath.endsWith('/api/v1/tenant/mcp')) {
        const requestBody = body ? (typeof body === 'string' ? JSON.parse(body) : body) : {};
        const rpcMethod = requestBody.method;

        console.log(` MOCKING API request to: ${fullUrlPath} with RPC method: ${rpcMethod} for <EMAIL>`);

        const mockProperty1 = {
          id: 1,
          street_address: '10 Downing Street',
          city: 'London',
          state: 'Greater London',
          postal_code: 'SW1A 2AA',
          country: 'GB',
          property_type: 'Flat',
          bedrooms: 3,
          bathrooms: 2,
          size: 150,
          size_unit: 'sqm'
        };

        const mockPaymentAgreement1: PaymentAgreement = {
          id: 101,
          agreement_id: "MOCK-AGMT-001",
          status: 'active',
          agreement_start: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString(),
          agreement_end: new Date(Date.now() + 265 * 24 * 60 * 60 * 1000).toISOString(),
          base_monthly_rent: "2500.00",
          base_monthly_fee_amount: "50.00",
          utilities_included: false,
          payout_day: "1",
          currency: 'GBP',
          tenant_id: 123,
          property_id: 1,
          entity_id: 10,
          agreement_type: 'standard_lease',
          payout_model: 'fixed_rent',
          operator_fee_amount: "0.00",
          operator_fee_percent: "0.00",
          operator_share: "0.00",
          owner_share: "100.00",
          tenant_fee_amount: "0.00",
          tenant_fee_percent: "0.00",
          tenant_plan: 'standard',
          vat_pc: "0.00",
          application_link: '',
          created_at: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date().toISOString(),
          deleted_at: null,
          addons: [],
          one_time_fees: [],
          property: mockProperty1
        };

        if (rpcMethod === 'tenant.get_payment_agreements') {
          return {
            success: true,
            result: {
              payment_agreements: [mockPaymentAgreement1] // Ensure this is not empty
            }
          };
        }

        if (rpcMethod === 'tenant.get_upcoming_payment') {
          return {
            success: true,
            result: {
              upcoming_payment: {
                amount: "2500.00",
                due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                property_name: mockProperty1.street_address,
                payment_agreement: mockPaymentAgreement1
              }
            }
          };
        }
        
        // Fallback for other RPC methods to /api/v1/tenant/mcp for the demo user if any
        console.warn(`Unhandled RPC method for demo user: ${rpcMethod} at ${fullUrlPath}`);
        return { success: true, result: {} }; // Return empty success to avoid breaking flow
      }
      // Add other `else if (fullUrlPath.endsWith('/some/other/endpoint'))` blocks here for demo user
    }
    
    // Original logic if not demo user or not a specifically mocked endpoint for demo user
    if (!token) {
      console.log('No auth token found, redirecting to login');
      router.replace('/(auth)/login');
      throw new Error('Authentication required. Please log in.');
    }
    
    // Get the full API URL with proxy
    const fullUrl = getFullApiUrl(url);
    console.log(`🌐 Making API request to: ${fullUrl}`);
    
    // Make the API request
    const response = await fetch(fullUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: body ? JSON.stringify(body) : undefined,
    });
    
    const data = await response.json();
    
    // Check for auth errors
    return handleApiResponse(data, logout);
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};
