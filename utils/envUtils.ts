/**
 * Environment variable utilities
 * Provides access to environment variables from .env file
 */
import Constants from 'expo-constants';

// Default values as fallbacks - only for non-critical values
const defaultValues = {
  PUBLIC_BUILDER_KEY: 'e522a61e738244a8bd0b5eb4fd39df80',
  // PROXY_URL has no fallback - it must be set in .env
};

/**
 * Get an environment variable
 * @param key The environment variable key
 * @returns The environment variable value or default
 */
export const getEnv = (key: string): string => {
  // First try to get from Expo Constants
  try {
    const expoExtra = Constants.expoConfig?.extra || Constants.manifest?.extra;
    if (expoExtra && expoExtra[key]) {
      return expoExtra[key];
    }
  } catch (e) {
    console.warn(`Error accessing Expo Constants for env var ${key}:`, e);
  }

  // For web, try to access process.env directly
  try {
    if (typeof process !== 'undefined' && process.env && process.env[key]) {
      return process.env[key] as string;
    }
  } catch (e) {
    console.warn(`Error accessing process.env for ${key}:`, e);
  }
  
  // Fallback to default values for non-critical variables
  return defaultValues[key as keyof typeof defaultValues] || '';
};

/**
 * Get the API base URL from environment
 * @returns The API base URL
 * @throws Error if PROXY_URL is not set
 */
export const getApiBaseUrl = (): string => {
  const proxyUrl = getEnv('PROXY_URL');
  if (!proxyUrl) {
    throw new Error('PROXY_URL environment variable is not set. Please set it in your .env file.');
  }
  return proxyUrl;
};
