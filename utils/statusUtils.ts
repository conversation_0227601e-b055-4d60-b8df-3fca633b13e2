/**
 * Constants and utilities for handling tenant statuses
 */

// Define tenant statuses with their colors
export const TENANT_STATUSES = {
  active: {
    bg: '#DCFCE7',
    text: '#166534'
  },
  scoring: {
    bg: '#DBEAFE',
    text: '#1E40AF'
  },
  signing: {
    bg: '#FEF9C3',
    text: '#854D0E'
  },
  closed: {
    bg: '#F3F4F6',
    text: '#4B5563'
  },
  pending: {
    bg: '#DBEAFE',
    text: '#1E40AF'
  },
  default: {
    bg: '#F3F4F6',
    text: '#4B5563'
  }
};

/**
 * Get the background color for a status
 * @param status The status string
 * @returns The background color hex code
 */
export function getStatusBgColor(status: string): string {
  const statusKey = status.toLowerCase() as keyof typeof TENANT_STATUSES;
  return TENANT_STATUSES[statusKey]?.bg || TENANT_STATUSES.default.bg;
}

/**
 * Get the text color for a status
 * @param status The status string
 * @returns The text color hex code
 */
export function getStatusTextColor(status: string): string {
  const statusKey = status.toLowerCase() as keyof typeof TENANT_STATUSES;
  return TENANT_STATUSES[statusKey]?.text || TENANT_STATUSES.default.text;
}

/**
 * Capitalize the first letter of a string
 * @param string The input string
 * @returns The string with first letter capitalized
 */
export function capitalizeFirstLetter(string: string): string {
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}

/**
 * Convert currency codes to symbols
 * @param currencyCode The currency code
 * @returns The currency symbol
 */
export function getCurrencySymbol(currencyCode?: string): string {
  if (!currencyCode) return '€'; // Default to Euro
  
  switch(currencyCode.toUpperCase()) {
    case 'EUR': return '€';
    case 'USD': return '$';
    case 'GBP': return '£';
    case 'JPY': return '¥';
    case 'CNY': return '¥';
    case 'RUB': return '₽';
    case 'INR': return '₹';
    default: return currencyCode; // If no symbol is found, return the code itself
  }
}
