export interface PaymentAgreement {
  id: number;
  agreement_id: string;
  status: string;
  agreement_start: string | null;
  agreement_end: string | null;
  base_monthly_rent: string;
  base_monthly_fee_amount: string;
  utilities_included: boolean;
  payout_day: string | null;
  currency: string;
  tenant_id: number;
  property_id: number;
  entity_id: number;
  agreement_type: string;
  payout_model: string;
  operator_fee_amount: string;
  operator_fee_percent: string;
  operator_share: string;
  owner_share: string;
  tenant_fee_amount: string;
  tenant_fee_percent: string;
  tenant_plan: string;
  vat_pc: string;
  application_link: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  addons: any[];
  one_time_fees: any[];
  property: {
    id: number;
    street_address: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    property_type: string;
    bedrooms: number;
    bathrooms: number;
    size: number;
    size_unit: string;
  };
  property_name?: string;
  property_address?: string;
  operator?: Operator;
}

// START: Added types for mock tenant data
export interface MockPropertyPaymentAgreement {
  id: number;
  property_id: number;
  tenant_id: number;
  status: string;
  rent_amount: number;
  rent_currency: string;
  payment_day: number;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  payment_setup_successful: boolean;
  direct_debit_mandate_id: string;
  next_payment_date: string | null;
  last_payment_status: string;
  last_payment_date: string | null;
}

export interface MockProperty {
  id: number;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  postcode: string;
  country: string;
  property_type: string;
  rent_amount: number;
  rent_currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  landlord_id: string;
  tenant_id: number;
  next_payment_date: string | null;
  last_payment_date: string | null;
  lease_start_date: string;
  lease_end_date: string;
  payment_day: number;
  payment_method_id: string | null;
  payment_agreements: MockPropertyPaymentAgreement[];
}

export interface MockPaymentMethodDetails {
  bank_name: string;
  account_holder_name: string;
  account_number_ending: string;
  mandate_id: string;
  status: string;
}

export interface MockPaymentMethod {
  id: string;
  tenant_id: number;
  type: string;
  details: MockPaymentMethodDetails;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface MockNotification {
  id: string;
  tenant_id: number;
  message: string;
  type: string;
  is_read: boolean;
  created_at: string;
}
// END: Added types for mock tenant data

// Dashboard-specific payment agreement interface
export interface DashboardPaymentAgreement {
  id: string;
  title: string;
  address: string;
  amount: number;
  utilities: boolean;
  status: string;
  pendingAction: string | null;
  validUntil: string | null;
  endDate: string | null;
  payment_agreement: PaymentAgreement;
}

export interface Operator {
  id: number;
  name: string;
  entity_type: string;
  phone: string;
  email?: string;
  payment_methods?: string[];
}

export interface UpcomingPayment {
  amount: string;
  dueDate: string;
  property: string;
  payment_agreement: PaymentAgreement;
}

export interface PaymentLine {
  description: string;
  quantity: number;
  unit_price: string;
  total_price: string;
}

export interface OutstandingPayment {
  id: number;
  total_amount: string;
  paid_amount: number;
  remaining_amount: number;
  due_date: string;
  status: string;
  lines: PaymentLine[];
  selected?: boolean; // For UI selection
}

export interface PaymentStatus {
  payment_id: string;
  status: string;
  amount: string;
  currency: string;
  payment_date: string;
  created_at: string;
  updated_at: string;
  completed: boolean;
}

export interface Tenant {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  birth_date: string | null;
  personal_code: string | null;
  language: string;
  status: string;
  current_street_address: string | null;
  current_city: string | null;
  current_state: string | null;
  current_postal_code: string | null;
  current_country: string | null;
  country_of_residence: string | null;
  employment_status: string | null;
  monthly_income: number | null;
  max_rent: number | null;
  bank: string | null;
  iban: string | null;
  created_at: string;
  updated_at: string;

  // Fields for mock data
  properties?: MockProperty[];
  payment_methods?: MockPaymentMethod[];
  notifications?: MockNotification[];
  currency?: string;
  locale?: string;
}
