import React from 'react';
import { StyledText, StyledView, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';

interface ScoreCardProps {
  tenantName: string;
  tenantScore: number;
  monthlyIncome: number;
  maxRent: number;
  paymentsOnTime: number;
  yearsOnCasaPay: number;
  verifications: {
    identity: boolean;
    income: boolean;
    phone: boolean;
    email: boolean;
  };
  details: {
    pets: string;
    age: string;
    languages: string;
    location: string;
  };
  onShareProfile?: () => void;
  onCopyLink?: () => void;
}

export const ScoreCard: React.FC<ScoreCardProps> = ({
  tenantName,
  tenantScore,
  monthlyIncome,
  maxRent,
  paymentsOnTime,
  yearsOnCasaPay,
  verifications,
  details,
  onShareProfile,
  onCopyLink,
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number): string => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  return (
    <StyledView className="bg-white rounded-xl shadow-lg p-4">
      {/* Header */}
      <StyledView className="items-center mb-4">
        <StyledView className="w-16 h-16 rounded-full bg-gray-200 items-center justify-center mb-3">
          <StyledText className="text-xl">👤</StyledText>
        </StyledView>
        <StyledText className="text-xl font-bold text-gray-900 mb-1">{tenantName}</StyledText>
        <StyledView className="flex-row items-center">
          <Feather name="shield" size={14} color="#4ca2f5" />
          <StyledText className="text-secondary font-medium ml-1 text-sm">CasaPay Premium</StyledText>
        </StyledView>
        <StyledView className="flex-row items-center mt-1">
          <Feather name="star" size={12} color="#fbbf24" />
          <StyledText className="text-gray-600 text-sm ml-1">{tenantScore / 20}.0</StyledText>
        </StyledView>
      </StyledView>

      {/* Key Metrics */}
      <StyledView className="flex-row justify-around mb-4">
        <StyledView className="items-center">
          <StyledText className="text-2xl font-bold text-gray-900">{yearsOnCasaPay}</StyledText>
          <StyledText className="text-xs text-gray-600 text-center">Years on CasaPay</StyledText>
        </StyledView>
        <StyledView className="items-center">
          <StyledText className="text-2xl font-bold text-gray-900">{paymentsOnTime}%</StyledText>
          <StyledText className="text-xs text-gray-600 text-center">Payments On Time</StyledText>
        </StyledView>
      </StyledView>

      {/* Verifications */}
      <StyledView className="mb-4">
        <StyledView className="flex-row items-center justify-between mb-3">
          <StyledText className="text-base font-bold text-gray-900">Verifications</StyledText>
          <StyledTouchableOpacity>
            <StyledText className="text-secondary text-sm">Edit</StyledText>
          </StyledTouchableOpacity>
        </StyledView>

        <StyledView className="space-y-2">
          <StyledView className="flex-row items-center">
            <Feather name="user" size={16} color="#374151" />
            <StyledText className="text-gray-900 ml-2 flex-1 text-sm">Identity</StyledText>
            {verifications.identity && <Feather name="check-circle" size={16} color="#10b981" />}
          </StyledView>

          <StyledView className="flex-row items-center">
            <StyledText className="text-lg mr-1">£</StyledText>
            <StyledText className="text-gray-900 ml-1 flex-1 text-sm">Income</StyledText>
            {verifications.income && <Feather name="check-circle" size={16} color="#10b981" />}
          </StyledView>

          <StyledView className="flex-row items-center">
            <Feather name="phone" size={16} color="#374151" />
            <StyledText className="text-gray-900 ml-2 flex-1 text-sm">Phone</StyledText>
            {verifications.phone && <Feather name="check-circle" size={16} color="#10b981" />}
          </StyledView>

          <StyledView className="flex-row items-center">
            <Feather name="mail" size={16} color="#374151" />
            <StyledText className="text-gray-900 ml-2 flex-1 text-sm">Email</StyledText>
            {verifications.email && <Feather name="check-circle" size={16} color="#10b981" />}
          </StyledView>
        </StyledView>
      </StyledView>

      {/* Details */}
      <StyledView className="mb-4">
        <StyledView className="flex-row items-center justify-between mb-3">
          <StyledText className="text-base font-bold text-gray-900">Details</StyledText>
          <StyledTouchableOpacity>
            <StyledText className="text-secondary text-sm">Edit</StyledText>
          </StyledTouchableOpacity>
        </StyledView>

        <StyledView className="space-y-2">
          <StyledView className="flex-row items-center">
            <StyledText className="text-base mr-2">🐾</StyledText>
            <StyledText className="text-gray-900 ml-1 text-sm">Pets: {details.pets}</StyledText>
          </StyledView>

          <StyledView className="flex-row items-center">
            <StyledText className="text-base mr-2">🎂</StyledText>
            <StyledText className="text-gray-900 ml-1 text-sm">Born in the {details.age}</StyledText>
          </StyledView>

          <StyledView className="flex-row items-center">
            <StyledText className="text-base mr-2">🗣️</StyledText>
            <StyledText className="text-gray-900 ml-1 text-sm">Speaks {details.languages}</StyledText>
          </StyledView>

          <StyledView className="flex-row items-center">
            <Feather name="map-pin" size={16} color="#374151" />
            <StyledText className="text-gray-900 ml-2 text-sm">Lives in {details.location}</StyledText>
          </StyledView>
        </StyledView>
      </StyledView>

      {/* Income Summary */}
      <StyledView className="flex-row justify-around mb-4 py-3 bg-gray-50 rounded-xl">
        <StyledView className="items-center">
          <StyledText className="text-xs text-gray-600 mb-1">Combined Monthly Income</StyledText>
          <StyledText className="text-lg font-bold text-gray-900">{formatCurrency(monthlyIncome)}</StyledText>
        </StyledView>
        <StyledView className="items-center">
          <StyledText className="text-xs text-gray-600 mb-1">Max Rent (40%)</StyledText>
          <StyledText className="text-lg font-bold text-gray-900">{formatCurrency(maxRent)}</StyledText>
        </StyledView>
      </StyledView>

      {/* Action Buttons */}
      <StyledView className="space-y-2">
        <StyledTouchableOpacity
          className="bg-white border-2 border-secondary rounded-xl py-2 px-4 items-center"
          onPress={onShareProfile}
        >
          <StyledText className="text-secondary text-sm font-semibold">Share profile</StyledText>
        </StyledTouchableOpacity>
      </StyledView>
    </StyledView>
  );
};
