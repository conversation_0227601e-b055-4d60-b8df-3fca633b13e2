import React from 'react';
import { Modal, Image } from 'react-native';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';

interface PhotoPreviewProps {
  isVisible: boolean;
  imageUri: string;
  captureType: 'selfie' | 'document';
  title: string;
  onUsePhoto: () => void;
  onRetake: () => void;
  onClose: () => void;
}

export const PhotoPreview: React.FC<PhotoPreviewProps> = ({
  isVisible,
  imageUri,
  captureType,
  title,
  onUsePhoto,
  onRetake,
  onClose
}) => {
  if (!isVisible || !imageUri) return null;

  return (
    <Modal visible={isVisible} animationType="slide" presentationStyle="fullScreen">
      <StyledView className="flex-1 bg-black">
        {/* Header */}
        <StyledView className="flex-row items-center justify-between p-4 bg-black/80 z-10">
          <StyledTouchableOpacity onPress={onClose} className="p-2">
            <Feather name="x" size={24} color="white" />
          </StyledTouchableOpacity>
          <StyledView className="flex-1 items-center">
            <StyledText className="text-white font-medium">Preview {title}</StyledText>
          </StyledView>
          <StyledView className="w-10" />
        </StyledView>

        {/* Photo Preview */}
        <StyledView className="flex-1 items-center justify-center p-4">
          <StyledView className="relative">
            <Image
              source={{ uri: imageUri }}
              style={{
                width: captureType === 'selfie' ? 300 : 350,
                height: captureType === 'selfie' ? 300 : 220,
                borderRadius: captureType === 'selfie' ? 150 : 12,
                transform: captureType === 'selfie' ? [{ scaleX: -1 }] : undefined
              }}
              resizeMode="cover"
            />
            
            {/* Quality indicator overlay */}
            <StyledView className="absolute top-3 right-3 bg-green-500 rounded-full px-3 py-1">
              <StyledText className="text-white text-xs font-medium">✓ Good Quality</StyledText>
            </StyledView>
          </StyledView>

          {/* Instructions */}
          <StyledView className="mt-6 px-6">
            <StyledText className="text-white text-center text-lg font-medium mb-2">
              How does this look?
            </StyledText>
            <StyledText className="text-white/70 text-center text-sm">
              {captureType === 'selfie' 
                ? 'Make sure your face is clearly visible and well-lit'
                : 'Ensure all text and details are clearly readable'
              }
            </StyledText>
          </StyledView>
        </StyledView>

        {/* Bottom Actions */}
        <StyledView className="p-6 bg-black/80">
          <StyledView className="flex-row space-x-4">
            {/* Retake Button */}
            <StyledTouchableOpacity
              className="flex-1 border border-white/30 rounded-xl py-4 px-6 items-center"
              onPress={onRetake}
            >
              <StyledView className="flex-row items-center">
                <Feather name="camera" size={20} color="white" />
                <StyledText className="text-white font-medium ml-2">Retake Photo</StyledText>
              </StyledView>
            </StyledTouchableOpacity>

            {/* Use Photo Button */}
            <StyledTouchableOpacity
              className="flex-1 bg-secondary rounded-xl py-4 px-6 items-center"
              onPress={onUsePhoto}
            >
              <StyledView className="flex-row items-center">
                <Feather name="check" size={20} color="white" />
                <StyledText className="text-white font-medium ml-2">Use This Photo</StyledText>
              </StyledView>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledView>
    </Modal>
  );
};
