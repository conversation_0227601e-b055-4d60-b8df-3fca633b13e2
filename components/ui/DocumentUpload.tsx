import React, { useState } from 'react';
import { Platform, Alert } from 'react-native';
import { StyledText, StyledView, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';

interface DocumentUploadProps {
  onUpload: (documents: DocumentUploadResult[]) => void;
  title: string;
  subtitle: string;
  acceptedTypes?: string[];
  maxFiles?: number;
  disabled?: boolean;
}

interface DocumentUploadResult {
  uri: string;
  name: string;
  size: number;
  type: string;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUpload,
  title,
  subtitle,
  acceptedTypes = ['application/pdf'],
  maxFiles = 3,
  disabled = false,
}) => {
  const [uploadedDocuments, setUploadedDocuments] = useState<DocumentUploadResult[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const handleDocumentPicker = async () => {
    if (disabled || isUploading) return;

    try {
      setIsUploading(true);

      const result = await DocumentPicker.getDocumentAsync({
        type: acceptedTypes,
        multiple: maxFiles > 1,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets) {
        const documents: DocumentUploadResult[] = result.assets.map(asset => ({
          uri: asset.uri,
          name: asset.name,
          size: asset.size || 0,
          type: asset.mimeType || 'application/pdf',
        }));

        // Validate file sizes (max 10MB per file)
        const oversizedFiles = documents.filter(doc => doc.size > 10 * 1024 * 1024);
        if (oversizedFiles.length > 0) {
          Alert.alert(
            'File Too Large',
            'Please select files smaller than 10MB each.',
            [{ text: 'OK' }]
          );
          return;
        }

        setUploadedDocuments(documents);
        onUpload(documents);
      }
    } catch (error) {
      console.error('Document picker error:', error);
      Alert.alert(
        'Upload Error',
        'Failed to select documents. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleWebFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const documents: DocumentUploadResult[] = Array.from(files).map(file => ({
      uri: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
      type: file.type,
    }));

    // Validate file sizes
    const oversizedFiles = documents.filter(doc => doc.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      Alert.alert(
        'File Too Large',
        'Please select files smaller than 10MB each.',
        [{ text: 'OK' }]
      );
      return;
    }

    setUploadedDocuments(documents);
    onUpload(documents);
  };

  const removeDocument = (index: number) => {
    const updatedDocuments = uploadedDocuments.filter((_, i) => i !== index);
    setUploadedDocuments(updatedDocuments);
    onUpload(updatedDocuments);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderUploadArea = () => {
    if (Platform.OS === 'web') {
      return (
        <StyledView>
          <input
            type="file"
            accept={acceptedTypes.join(',')}
            multiple={maxFiles > 1}
            onChange={handleWebFileUpload}
            style={{ display: 'none' }}
            id="document-upload"
            disabled={disabled || isUploading}
          />
          <StyledTouchableOpacity
            className={`w-full border-2 border-dashed rounded-xl p-8 items-center transition-colors ${
              disabled || isUploading
                ? 'border-gray-200 bg-gray-50'
                : 'border-gray-300 bg-white hover:border-secondary hover:bg-secondary/5'
            }`}
            onPress={() => {
              if (!disabled && !isUploading) {
                document.getElementById('document-upload')?.click();
              }
            }}
            disabled={disabled || isUploading}
          >
            <Feather 
              name="upload-cloud" 
              size={48} 
              color={disabled || isUploading ? '#9CA3AF' : '#4ca2f5'} 
            />
            <StyledText className={`text-lg font-medium mt-4 mb-2 ${
              disabled || isUploading ? 'text-gray-400' : 'text-gray-900'
            }`}>
              {title}
            </StyledText>
            <StyledText className={`text-base text-center ${
              disabled || isUploading ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {subtitle}
            </StyledText>
            <StyledText className={`text-sm mt-2 ${
              disabled || isUploading ? 'text-gray-400' : 'text-gray-500'
            }`}>
              PDF files up to 10MB each
            </StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      );
    }

    return (
      <StyledTouchableOpacity
        className={`w-full border-2 border-dashed rounded-xl p-8 items-center transition-colors ${
          disabled || isUploading
            ? 'border-gray-200 bg-gray-50'
            : 'border-gray-300 bg-white hover:border-secondary hover:bg-secondary/5'
        }`}
        onPress={handleDocumentPicker}
        disabled={disabled || isUploading}
      >
        <Feather 
          name="upload-cloud" 
          size={48} 
          color={disabled || isUploading ? '#9CA3AF' : '#4ca2f5'} 
        />
        <StyledText className={`text-lg font-medium mt-4 mb-2 ${
          disabled || isUploading ? 'text-gray-400' : 'text-gray-900'
        }`}>
          {title}
        </StyledText>
        <StyledText className={`text-base text-center ${
          disabled || isUploading ? 'text-gray-400' : 'text-gray-600'
        }`}>
          {subtitle}
        </StyledText>
        <StyledText className={`text-sm mt-2 ${
          disabled || isUploading ? 'text-gray-400' : 'text-gray-500'
        }`}>
          PDF files up to 10MB each
        </StyledText>
      </StyledTouchableOpacity>
    );
  };

  return (
    <StyledView className="w-full">
      {uploadedDocuments.length === 0 ? (
        renderUploadArea()
      ) : (
        <StyledView>
          {/* Uploaded Documents List */}
          <StyledView className="space-y-3 mb-4">
            {uploadedDocuments.map((doc, index) => (
              <StyledView
                key={index}
                className="flex-row items-center justify-between bg-green-50 border border-green-200 rounded-xl p-4"
              >
                <StyledView className="flex-row items-center flex-1">
                  <Feather name="file-text" size={24} color="#10b981" />
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-green-900 font-medium" numberOfLines={1}>
                      {doc.name}
                    </StyledText>
                    <StyledText className="text-green-700 text-sm">
                      {formatFileSize(doc.size)}
                    </StyledText>
                  </StyledView>
                </StyledView>
                <StyledTouchableOpacity
                  onPress={() => removeDocument(index)}
                  className="ml-3 p-1"
                  disabled={disabled}
                >
                  <Feather name="x" size={20} color="#ef4444" />
                </StyledTouchableOpacity>
              </StyledView>
            ))}
          </StyledView>

          {/* Add More Button (if under max files) */}
          {uploadedDocuments.length < maxFiles && (
            <StyledTouchableOpacity
              className="w-full border border-secondary rounded-xl p-4 items-center flex-row justify-center"
              onPress={Platform.OS === 'web' ? () => {
                document.getElementById('document-upload')?.click();
              } : handleDocumentPicker}
              disabled={disabled || isUploading}
            >
              <Feather name="plus" size={20} color="#4ca2f5" />
              <StyledText className="text-secondary font-medium ml-2">
                Add More Documents
              </StyledText>
            </StyledTouchableOpacity>
          )}

          {/* Hidden file input for web */}
          {Platform.OS === 'web' && (
            <input
              type="file"
              accept={acceptedTypes.join(',')}
              multiple={maxFiles > 1}
              onChange={handleWebFileUpload}
              style={{ display: 'none' }}
              id="document-upload"
              disabled={disabled || isUploading}
            />
          )}
        </StyledView>
      )}
    </StyledView>
  );
};
