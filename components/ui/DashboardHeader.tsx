import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useAuth } from "@/contexts/AuthContext";
import { StyledText, StyledView } from "./StyledComponents";
import { Logo } from "./Logo";

interface DashboardHeaderProps {
  onAvatarPress?: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  onAvatarPress,
}) => {
  const { state } = useAuth();
  const userInitials = state.user?.first_name && state.user?.last_name
    ? `${state.user.first_name.charAt(0)}${state.user.last_name.charAt(0)}`.toUpperCase()
    : state.user?.first_name?.charAt(0).toUpperCase() || 'U';

  return (
    <StyledView className="w-full h-16 border-b border-gray/50 justify-center items-center bg-white">
      <StyledView className="w-full h-16 px-6 py-4 flex-row justify-between items-center">
        <Logo size="small" textClass="text-xl font-bold text-primary" />

        <TouchableOpacity
          onPress={onAvatarPress}
          className="w-8 h-8 rounded-full bg-secondary justify-center items-center"
        >
          <StyledText className="text-white text-sm font-medium">
            {userInitials}
          </StyledText>
        </TouchableOpacity>
      </StyledView>
    </StyledView>
  );
};
