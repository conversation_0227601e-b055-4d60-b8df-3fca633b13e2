import React from 'react';
import { Text, Platform } from 'react-native';

// Platform detection for proper typography rendering
const isWebPlatform = Platform.OS === 'web' || typeof window !== 'undefined';

// Header text component with consistent large typography
export const HeaderText: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  if (isWebPlatform) {
    return (
      <Text className="text-4xl font-bold text-gray-900 mb-3 leading-tight">
        {children}
      </Text>
    );
  } else {
    // React Native: Use explicit style object for guaranteed large text
    return (
      <Text style={{
        fontSize: 32,
        lineHeight: 38,
        fontWeight: '700',
        color: '#111827', // gray-900
        marginBottom: 12,
        textAlign: 'left'
      }}>
        {children}
      </Text>
    );
  }
};

// Subtitle text component with consistent medium typography
export const SubtitleText: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  if (isWebPlatform) {
    return (
      <Text className="text-lg text-gray-600 leading-relaxed">
        {children}
      </Text>
    );
  } else {
    // React Native: Use explicit style object for guaranteed styling
    return (
      <Text style={{
        fontSize: 18,
        lineHeight: 26,
        color: '#4B5563', // gray-600
        marginBottom: 8,
        textAlign: 'left'
      }}>
        {children}
      </Text>
    );
  }
};
