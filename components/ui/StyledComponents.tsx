import React from 'react';
import { Text, View, TextInput, TouchableOpacity, ScrollView, SafeAreaView, Image, TextProps, ViewProps, TextInputProps, TouchableOpacityProps } from 'react-native';

// Define types for our styled components
type StyledTextProps = TextProps & {
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
  className?: string;
};

type StyledViewProps = ViewProps & {
  className?: string;
};

type StyledTextInputProps = TextInputProps & {
  label?: string;
  error?: string;
  className?: string;
};

type StyledTouchableOpacityProps = TouchableOpacityProps & {
  variant?: 'primary' | 'secondary' | 'outline' | 'link';
  className?: string;
};

// Styled Text component with different text types
export const StyledText: React.FC<StyledTextProps> = ({ style, type = 'default', className = '', ...rest }) => {
  // Define Tailwind classes based on text type
  let typeClasses = '';
  switch (type) {
    case 'default':
      typeClasses = 'text-base leading-6 text-body';
      break;
    case 'defaultSemiBold':
      typeClasses = 'text-base leading-6 font-semibold text-body';
      break;
    case 'title':
      typeClasses = 'text-3xl font-bold leading-8 text-primary';
      break;
    case 'subtitle':
      typeClasses = 'text-xl font-bold text-primary';
      break;
    case 'link':
      typeClasses = 'text-base leading-7 text-link';
      break;
  }

  return (
    <Text
      className={`${typeClasses} ${className}`}
      style={style}
      {...rest}
    />
  );
};

// Styled View component
export const StyledView: React.FC<StyledViewProps> = ({ className = '', ...rest }) => {
  return <View className={className} {...rest} />;
};

// Styled TextInput component with label and error handling
export const StyledTextInput: React.FC<StyledTextInputProps> = ({
  label,
  error,
  className = '',
  ...rest
}) => {
  return (
    <View className="mb-8">
      {label && (
        <Text className="text-sm font-medium text-gray-700 mb-2">{label}</Text>
      )}
      <TextInput
        className={`border rounded-xl px-4 py-3 bg-white transition-colors focus:border-secondary focus:ring-2 focus:ring-secondary/20 focus:outline-none ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300'} ${className}`}
        placeholderTextColor="#9B9B9B"
        {...rest}
      />
      {error && (
        <Text className="text-red-500 text-sm mt-1">{error}</Text>
      )}
    </View>
  );
};

// Styled TouchableOpacity component with different variants
export const StyledTouchableOpacity: React.FC<StyledTouchableOpacityProps> = ({
  variant = 'primary',
  className = '',
  disabled,
  ...rest
}) => {
  // Define Tailwind classes based on button variant
  let variantClasses = '';
  switch (variant) {
    case 'primary':
      variantClasses = 'bg-primary rounded-xl py-3 items-center';
      break;
    case 'secondary':
      variantClasses = 'bg-secondary rounded-xl py-3 items-center';
      break;
    case 'outline':
      variantClasses = 'bg-white border border-dark-border rounded-xl py-3 items-center';
      break;
    case 'link':
      variantClasses = 'py-2';
      break;
  }

  return (
    <TouchableOpacity
      className={`${variantClasses} ${disabled ? 'opacity-70' : ''} ${className}`}
      disabled={disabled}
      {...rest}
    />
  );
};

// Re-export other components
export const StyledScrollView = ScrollView;
export const StyledSafeAreaView = SafeAreaView;
export const StyledImage = Image;
