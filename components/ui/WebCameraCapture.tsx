import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';
import { PhotoPreview } from './PhotoPreview';

interface WebCameraCaptureProps {
  isVisible: boolean;
  onClose: () => void;
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
}

export const WebCameraCapture: React.FC<WebCameraCaptureProps> = ({
  isVisible,
  onClose,
  onCapture,
  captureType,
  title,
  subtitle
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (isVisible && typeof navigator !== 'undefined' && navigator.mediaDevices) {
      // Reset states when opening camera
      setIsVideoReady(false);
      setCapturedImage(null);
      setShowPreview(false);
      startCamera();
    }

    return () => {
      stopCamera();
    };
  }, [isVisible]);

  const startCamera = async () => {
    try {
      // Enhanced constraints for better macOS compatibility
      const constraints = {
        video: {
          facingMode: captureType === 'selfie' ? 'user' : 'environment',
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          frameRate: { ideal: 30, min: 15 },
          // Additional constraints for macOS Safari compatibility
          aspectRatio: { ideal: 16/9 },
          resizeMode: 'crop-and-scale'
        },
        audio: false
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      setStream(mediaStream);
      setHasPermission(true);

      if (videoRef.current) {
        const video = videoRef.current;

        // Set up event handlers BEFORE assigning srcObject
        const handleLoadStart = () => {
          setIsVideoReady(false);
        };

        const handleLoadedMetadata = () => {
          if (video.readyState >= 1) {
            video.play()
              .then(() => {
                // Video play started successfully
              })
              .catch((error) => {
                console.error('Error playing video:', error);
                // Try to set ready anyway in case autoplay restrictions
                setTimeout(() => {
                  if (video.videoWidth > 0 && video.videoHeight > 0) {
                    setIsVideoReady(true);
                  }
                }, 1500);
              });
          }
        };

        const handleCanPlay = () => {
          // Video can start playing
        };

        // Primary fix: Use 'playing' event as main trigger for video readiness
        const handlePlaying = () => {
          // Check if video has valid dimensions before setting ready
          if (video.videoWidth > 0 && video.videoHeight > 0) {
            // Increased delay for ARM64 Chrome rendering
            setTimeout(() => {
              setIsVideoReady(true);
            }, 1200);
          } else {
            // If dimensions not ready, wait a bit more
            setTimeout(() => {
              if (video.videoWidth > 0 && video.videoHeight > 0) {
                setIsVideoReady(true);
              }
            }, 2000);
          }
        };

        // Additional confirmation that frames are actively rendering
        const handleTimeUpdate = () => {
          if (!isVideoReady && video.currentTime > 0 && video.videoWidth > 0 && video.videoHeight > 0) {
            setIsVideoReady(true);
          }
        };

        const handleError = (error: any) => {
          console.error('Video error:', error);
          setHasPermission(false);
        };

        // Remove any existing event listeners
        video.onloadedmetadata = null;
        video.oncanplay = null;
        video.onloadstart = null;
        video.onplaying = null;
        video.ontimeupdate = null;
        video.onerror = null;

        // Set up event listeners
        video.addEventListener('loadstart', handleLoadStart);
        video.addEventListener('loadedmetadata', handleLoadedMetadata);
        video.addEventListener('canplay', handleCanPlay);
        video.addEventListener('playing', handlePlaying); // Primary trigger
        video.addEventListener('timeupdate', handleTimeUpdate); // Additional confirmation
        video.addEventListener('error', handleError);

        // Set srcObject and explicitly load
        video.srcObject = mediaStream;
        video.load(); // Explicitly trigger load

        // Enhanced fallback: Check multiple conditions
        setTimeout(() => {
          if (!isVideoReady) {
            if (video.readyState >= 2 && video.videoWidth > 0 && video.videoHeight > 0) {
              setIsVideoReady(true);
            } else if (video.currentTime > 0) {
              setIsVideoReady(true);
            }
          }
        }, 4000);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setHasPermission(false);
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }

    // Clean up video element and event listeners
    if (videoRef.current) {
      const video = videoRef.current;
      video.srcObject = null;

      // Remove all event listeners
      video.onloadstart = null;
      video.onloadedmetadata = null;
      video.oncanplay = null;
      video.onplaying = null;
      video.ontimeupdate = null;
      video.onerror = null;

      // Also remove addEventListener listeners (though they should be cleaned up automatically)
      const emptyHandler = () => {};
      video.removeEventListener('loadstart', emptyHandler);
      video.removeEventListener('loadedmetadata', emptyHandler);
      video.removeEventListener('canplay', emptyHandler);
      video.removeEventListener('playing', emptyHandler);
      video.removeEventListener('timeupdate', emptyHandler);
      video.removeEventListener('error', emptyHandler);
    }

    setIsVideoReady(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || isLoading || !isVideoReady) return;

    try {
      setIsLoading(true);
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) return;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to blob and create URL
      canvas.toBlob((blob) => {
        if (blob) {
          const imageUrl = URL.createObjectURL(blob);
          setCapturedImage(imageUrl);
          setShowPreview(true);
        }
      }, 'image/jpeg', 0.8);
    } catch (error) {
      console.error('Error capturing photo:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUsePhoto = () => {
    if (capturedImage) {
      onCapture(capturedImage);
      setShowPreview(false);
      onClose();
    }
  };

  const handleRetakePhoto = () => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
    setShowPreview(false);
  };

  const handleClose = () => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage);
      setCapturedImage(null);
    }
    setShowPreview(false);
    onClose();
  };



  if (!isVisible) return null;

  return (
    <>
      <Modal visible={isVisible && !showPreview} animationType="slide" presentationStyle="fullScreen">
        <StyledView className="flex-1 bg-black">
          {/* Header */}
          <StyledView className="flex-row items-center justify-between p-4 bg-black/80 z-10">
            <StyledTouchableOpacity onPress={handleClose} className="p-2">
              <Feather name="x" size={24} color="white" />
            </StyledTouchableOpacity>
            <StyledView className="flex-1 items-center">
              <StyledText className="text-white font-medium">{title}</StyledText>
            </StyledView>
            <StyledView className="w-10" />
          </StyledView>

        {hasPermission === null && (
          <StyledView className="flex-1 items-center justify-center">
            <StyledText className="text-white">Requesting camera access...</StyledText>
          </StyledView>
        )}

        {hasPermission === false && (
          <StyledView className="flex-1 items-center justify-center p-6">
            <Feather name="camera-off" size={64} color="white" className="mb-6" />
            <StyledText className="text-white text-center mb-4 text-xl font-medium">
              Camera Access Required
            </StyledText>
            <StyledText className="text-white/70 text-center mb-8 text-base">
              This feature requires camera access to take live photos. Please allow camera access in your browser settings and try again.
            </StyledText>

            <StyledView className="w-full max-w-sm">
              <StyledTouchableOpacity
                className="bg-secondary rounded-xl py-4 px-6 items-center mb-4"
                onPress={startCamera}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="camera" size={20} color="white" />
                  <StyledText className="text-white font-medium ml-2">Try Camera Again</StyledText>
                </StyledView>
              </StyledTouchableOpacity>

              <StyledTouchableOpacity
                className="border border-white/30 rounded-xl py-4 px-6 items-center"
                onPress={onClose}
              >
                <StyledText className="text-white/70 font-medium">Cancel</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        )}

        {hasPermission === true && (
          <>
            {/* Camera View */}
            <StyledView className="flex-1 relative">
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                autoPlay
                playsInline
                muted
                style={{
                  transform: captureType === 'selfie' ? 'scaleX(-1)' : 'none',
                  backgroundColor: '#000',
                  opacity: isVideoReady ? 1 : 0
                }}
              />

              {/* Loading overlay while video initializes */}
              {!isVideoReady && (
                <StyledView className="absolute inset-0 bg-black items-center justify-center">
                  <StyledView className="items-center">
                    <StyledView className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mb-4" />
                    <StyledText className="text-white text-lg font-medium">Initializing Camera...</StyledText>
                    <StyledText className="text-white/70 text-sm mt-2">Please wait a moment</StyledText>
                  </StyledView>
                </StyledView>
              )}

              {/* Capture Area Overlay - only show when video is ready */}
              {isVideoReady && (
                <StyledView className="absolute inset-0 items-center justify-center pointer-events-none">
                  {captureType === 'selfie' ? (
                    <StyledView
                      className="border-2 border-white/50 rounded-full"
                      style={{ width: 256, height: 256 }}
                    />
                  ) : (
                    <StyledView
                      className="border-2 border-white/50 rounded-lg"
                      style={{
                        width: 900,
                        height: 563,
                        maxWidth: '90vw',
                        maxHeight: '60vh'
                      }}
                    />
                  )}
                </StyledView>
              )}

              {/* Instructions - only show when video is ready */}
              {isVideoReady && (
                <StyledView className="absolute bottom-32 left-0 right-0 items-center">
                  <StyledView className="bg-black/70 rounded-lg px-4 py-2">
                    <StyledText className="text-white text-center text-sm">
                      {captureType === 'selfie'
                        ? 'Position your face in the circle'
                        : 'Fill the frame with your document for best quality'
                      }
                    </StyledText>
                  </StyledView>
                </StyledView>
              )}
            </StyledView>

            {/* Bottom Controls */}
            <StyledView className="flex-row items-center justify-center p-6 bg-black/80">
              <StyledTouchableOpacity
                onPress={capturePhoto}
                disabled={isLoading || !isVideoReady}
                className={`w-20 h-20 rounded-full items-center justify-center shadow-lg ${
                  isVideoReady && !isLoading ? 'bg-white' : 'bg-gray-500'
                }`}
              >
                {isLoading ? (
                  <StyledView className="w-16 h-16 bg-gray-300 rounded-full animate-pulse" />
                ) : (
                  <StyledView className={`w-16 h-16 border-4 rounded-full ${
                    isVideoReady ? 'bg-white border-gray-300' : 'bg-gray-400 border-gray-500'
                  }`} />
                )}
              </StyledTouchableOpacity>
            </StyledView>

            {/* Hidden canvas for photo capture */}
            <canvas ref={canvasRef} className="hidden" />
          </>
        )}
        </StyledView>
      </Modal>

      {/* Photo Preview Modal */}
      <PhotoPreview
        isVisible={showPreview}
        imageUri={capturedImage || ''}
        captureType={captureType}
        title={title}
        onUsePhoto={handleUsePhoto}
        onRetake={handleRetakePhoto}
        onClose={handleClose}
      />
    </>
  );
};
