import React, { useState } from 'react';
import { Image, Text, View, Platform } from 'react-native';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  textClass?: string;
}

export const Logo: React.FC<LogoProps> = ({
  size = 'medium',
  showText = false,
  textClass = "text-xl font-bold text-primary"
}) => {
  const [imageError, setImageError] = useState(false);

  const getSizeStyles = () => {
    const baseStyles = {
      // Add image quality settings for better rendering
      ...(Platform.OS === 'web' && {
        imageRendering: 'crisp-edges' as any,
        WebkitImageRendering: '-webkit-optimize-contrast' as any,
      })
    };

    switch (size) {
      case 'small':
        return { ...baseStyles, width: 120, height: 48 };
      case 'large':
        return { ...baseStyles, width: 240, height: 96 };
      default: // medium
        return { ...baseStyles, width: 180, height: 72 };
    }
  };

  // Use different approaches for web vs native
  const getLogoSource = () => {
    if (Platform.OS === 'web') {
      return { uri: '/logo.png' };
    } else {
      return require('../../assets/images/CasaPay_Logo.png');
    }
  };

  if (imageError) {
    return (
      <Text className={textClass}>
        CasaPay
      </Text>
    );
  }

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <Image
        source={getLogoSource()}
        style={getSizeStyles()}
        resizeMode="contain"
        onError={() => setImageError(true)}
        accessibilityLabel="CasaPay Logo"
        // Add high-DPI handling for better quality
        {...(Platform.OS !== 'web' && {
          fadeDuration: 0, // Disable fade for crisp loading
        })}
      />
      {showText && (
        <Text className={`ml-2 ${textClass}`}>
          CasaPay
        </Text>
      )}
    </View>
  );
};