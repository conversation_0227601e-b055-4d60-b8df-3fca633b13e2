import React from "react";
import { View, TouchableOpacity } from "react-native";
import { StyledText, StyledView } from "./StyledComponents";

export interface Property {
  id: string;
  address: string;
  city: string;
  status: "scoring" | "signing" | "active" | "closed";
  statusText: string;
  rent: string;
  utilities: "included" | "extra";
}

interface PropertyListProps {
  properties: Property[];
  onPropertyPress?: (property: Property) => void;
}

const StatusBadge: React.FC<{ status: Property["status"]; text: string }> = ({
  status,
  text,
}) => {
  const getBadgeColors = () => {
    switch (status) {
      case "scoring":
        return "bg-info-accent text-info";
      case "signing":
        return "bg-warning-accent text-warning";
      case "active":
        return "bg-success-accent text-success";
      case "closed":
        return "bg-gray text-dark-gray";
      default:
        return "bg-gray text-dark-gray";
    }
  };

  return (
    <StyledView className={`px-2 py-1 rounded-full ${getBadgeColors()}`}>
      <StyledText className="text-xs font-medium">{text}</StyledText>
    </StyledView>
  );
};

export const PropertyList: React.FC<PropertyListProps> = ({
  properties,
  onPropertyPress,
}) => {
  return (
    <StyledView className="w-full">
      <StyledText type="subtitle" className="mb-6">
        Payment Agreements
      </StyledText>

      <StyledView className="flex-col gap-4">
        {properties.map((property) => (
          <TouchableOpacity
            key={property.id}
            onPress={() => onPropertyPress?.(property)}
            className="w-full p-4 flex-row justify-between items-center bg-white border border-gray rounded-xl"
          >
            <StyledView className="flex-col gap-3">
              <StyledView className="flex-row items-center gap-3">
                <StyledText>🏠</StyledText>
                <StyledText type="defaultSemiBold">
                  {property.address}
                </StyledText>
                <StatusBadge
                  status={property.status}
                  text={property.statusText}
                />
              </StyledView>

              <StyledView className="flex-col gap-2">
                <StyledText className="text-dark-gray text-sm">
                  {property.city}
                </StyledText>
              </StyledView>
            </StyledView>

            <StyledView className="items-end">
              <StyledText type="defaultSemiBold">{property.rent}</StyledText>
              <StyledText className="text-dark text-sm">
                {property.utilities === "included"
                  ? "Utilities included"
                  : "+ utilities"}
              </StyledText>
            </StyledView>
          </TouchableOpacity>
        ))}
      </StyledView>
    </StyledView>
  );
};
