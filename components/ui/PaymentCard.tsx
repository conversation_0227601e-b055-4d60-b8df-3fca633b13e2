import React from "react";
import { View, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import {
  StyledText,
  StyledView,
  StyledTouchableOpacity,
} from "./StyledComponents";

interface PaymentCardProps {
  amount: string;
  dueDate: string;
  property: string;
}

export const PaymentCard: React.FC<PaymentCardProps> = ({
  amount,
  dueDate,
  property,
}) => {
  const handlePayNow = () => {
    router.push({
      pathname: "/(dashboard)/payment",
      params: { amount, dueDate, property },
    });
  };

  return (
    <StyledView className="w-full p-6 flex-row justify-between items-center rounded-xl border border-secondary/20 bg-white">
      <StyledView className="flex-row items-center gap-4">
        <StyledView className="w-12 h-12 rounded-full bg-accent justify-center items-center">
          <StyledText className="text-xl">⏰</StyledText>
        </StyledView>

        <StyledView className="flex-col gap-1">
          <StyledText className="text-secondary text-sm">
            Upcoming Payment
          </StyledText>
          <StyledText type="defaultSemiBold" className="text-lg">
            {amount} due on {dueDate}
          </StyledText>
          <StyledText className="text-dark-gray text-sm">{property}</StyledText>
        </StyledView>
      </StyledView>

      <StyledTouchableOpacity
        variant="secondary"
        onPress={handlePayNow}
        className="px-8 py-3.5"
      >
        <StyledText className="text-white font-medium">Pay Now</StyledText>
      </StyledTouchableOpacity>
    </StyledView>
  );
};
