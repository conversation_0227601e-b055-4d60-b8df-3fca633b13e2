import React, { useState } from 'react';
import { Platform, Image } from 'react-native';
import { StyledView, StyledText, StyledTouchableOpacity } from './StyledComponents';
import { Feather } from '@expo/vector-icons';
import { CameraCapture } from './CameraCapture';
import { WebCameraCapture } from './WebCameraCapture';

interface PhotoCaptureProps {
  onCapture: (imageUri: string) => void;
  captureType: 'selfie' | 'document';
  title: string;
  subtitle: string;
  hasPhoto: boolean;
  imageUri?: string;
  disabled?: boolean;
}

export const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  onCapture,
  captureType,
  title,
  subtitle,
  hasPhoto,
  imageUri,
  disabled = false
}) => {
  const [showCamera, setShowCamera] = useState(false);

  const handleCameraCapture = (imageUri: string) => {
    onCapture(imageUri);
    setShowCamera(false);
  };

  const CameraComponent = Platform.OS === 'web' ? WebCameraCapture : CameraCapture;

  return (
    <>
      <StyledView className="items-center">
        {/* Photo Icon */}
        <StyledView className="w-24 h-24 bg-gray-100 rounded-full items-center justify-center mb-6">
          <Feather
            name={captureType === 'selfie' ? 'user' : 'file-text'}
            size={32}
            color="#9CA3AF"
          />
        </StyledView>

        {/* Title and Subtitle */}
        <StyledView className="mb-8">
          <StyledText className="text-xl font-bold text-center mb-2">{title}</StyledText>
          <StyledText className="text-gray-600 text-center">{subtitle}</StyledText>
        </StyledView>

        {/* Action Buttons */}
        <StyledView className="w-full">
          {/* Primary Action - Camera Only */}
          <StyledTouchableOpacity
            className="bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-8 items-center mb-4 shadow-md transition-colors"
            onPress={() => setShowCamera(true)}
            disabled={disabled}
          >
            <StyledView className="flex-row items-center">
              <Feather name="camera" size={20} color="white" />
              <StyledText className="text-white font-medium ml-2">
                {hasPhoto ? 'Retake Photo' : 'Take Photo'}
              </StyledText>
            </StyledView>
          </StyledTouchableOpacity>

          {/* Success State with Photo Thumbnail */}
          {hasPhoto && imageUri && (
            <StyledView className="bg-green-50 border border-green-200 rounded-lg p-4">
              <StyledView className="flex-row items-center">
                {/* Photo Thumbnail */}
                <StyledView className="mr-3">
                  <Image
                    source={{ uri: imageUri }}
                    style={{
                      width: captureType === 'selfie' ? 48 : 60,
                      height: captureType === 'selfie' ? 48 : 38,
                      borderRadius: captureType === 'selfie' ? 24 : 6,
                      transform: captureType === 'selfie' ? [{ scaleX: -1 }] : undefined
                    }}
                    resizeMode="cover"
                  />
                </StyledView>

                {/* Success Text */}
                <StyledView className="flex-1">
                  <StyledText className="text-green-700 text-sm font-medium">
                    ✓ {captureType === 'selfie' ? 'Selfie' : 'Document'} captured successfully
                  </StyledText>
                  <StyledText className="text-green-600 text-xs mt-1">
                    Photo ready for verification
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          )}
        </StyledView>
      </StyledView>

      {/* Camera Modal */}
      <CameraComponent
        isVisible={showCamera}
        onClose={() => setShowCamera(false)}
        onCapture={handleCameraCapture}
        captureType={captureType}
        title={title}
        subtitle={subtitle}
      />
    </>
  );
};
