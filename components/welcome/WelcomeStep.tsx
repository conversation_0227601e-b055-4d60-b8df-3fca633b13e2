import React from 'react';
import { Image } from 'react-native';
import { StyledText, StyledView, StyledTouchableOpacity } from '../ui/StyledComponents';

interface WelcomeStepProps {
  imageSource: any; // Adjust type as per your image source (e.g., ImageSourcePropType)
  onGetStarted?: () => void;
  onLogin?: () => void;
  onRegister?: () => void;
  showDualButtons?: boolean;
}

const WelcomeStep: React.FC<WelcomeStepProps> = ({
  imageSource,
  onGetStarted,
  onLogin,
  onRegister,
  showDualButtons = false,
}) => {
  return (
    <StyledView className="w-full flex-1 items-center justify-center px-5 bg-white">
      <Image
        source={imageSource}
        className="w-4/5 mb-16"
        style={{ height: 320 }}
        resizeMode="contain"
      />

      {showDualButtons ? (
        <StyledView className="flex-row w-full gap-3 mt-5">
          <StyledTouchableOpacity
            onPress={onLogin}
            className="flex-1 bg-white border-2 border-secondary rounded-xl py-4 px-6 items-center shadow-md transition-colors hover:bg-gray-50"
          >
            <StyledText className="text-secondary text-base font-semibold">Log in</StyledText>
          </StyledTouchableOpacity>
          <StyledTouchableOpacity
            onPress={onRegister}
            className="flex-1 bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-6 items-center shadow-md transition-colors"
          >
            <StyledText className="text-white text-base font-semibold">Register</StyledText>
          </StyledTouchableOpacity>
        </StyledView>
      ) : (
        <StyledTouchableOpacity
          onPress={onGetStarted}
          className="bg-secondary hover:bg-secondary/90 rounded-xl py-4 px-6 items-center w-4/5 shadow-md transition-colors mt-5"
        >
          <StyledText className="text-white text-base font-semibold">Get started</StyledText>
        </StyledTouchableOpacity>
      )}
    </StyledView>
  );
};

export default WelcomeStep;
