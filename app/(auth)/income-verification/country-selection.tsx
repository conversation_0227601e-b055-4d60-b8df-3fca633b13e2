import React, { useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

const SUPPORTED_COUNTRIES = [
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'UK', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
];

export default function CountrySelection() {
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleContinue = async () => {
    if (!selectedCountry) return;

    setIsLoading(true);

    try {
      router.push({
        pathname: '/(auth)/income-verification/bank-selection',
        params: { country: selectedCountry }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const canProceed = (): boolean => {
    return !!selectedCountry;
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Select Your Country</HeaderText>
              <SubtitleText>Choose your country to see available banks</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {SUPPORTED_COUNTRIES.map((country) => (
                <StyledTouchableOpacity
                  key={country.code}
                  className={`w-full border rounded-xl p-4 mb-3 transition-colors ${
                    selectedCountry === country.code
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => setSelectedCountry(country.code)}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledView className="flex-row items-center">
                      <StyledText className="text-2xl mr-3">{country.flag}</StyledText>
                      <StyledText className={`text-base ${
                        selectedCountry === country.code ? 'text-secondary font-medium' : 'text-gray-900'
                      }`}>
                        {country.name}
                      </StyledText>
                    </StyledView>
                    {selectedCountry === country.code && (
                      <Feather name="check" size={20} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
            </StyledView>
          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Continue
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
