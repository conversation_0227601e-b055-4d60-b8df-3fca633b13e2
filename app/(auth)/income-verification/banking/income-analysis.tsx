import React, { useState, useEffect } from 'react';
import { ActivityIndicator, Modal } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

interface BankAccount {
  id: string;
  bankName: string;
  iban: string;
  monthlyIncome: number;
  averageIncome: number;
  incomeStability: number;
  lastPayment: string;
  paymentFrequency: string;
}

interface IncomeData {
  accounts: BankAccount[];
  totalMonthlyIncome: number;
  totalAverageIncome: number;
  overallStability: number;
}

// Utility function to mask IBAN for security
const maskIban = (iban: string): string => {
  if (iban.length < 8) return iban;
  const start = iban.substring(0, 4);
  const end = iban.substring(iban.length - 4);
  const middle = '*'.repeat(Math.max(0, iban.length - 8));
  return `${start} ${middle} ${end}`;
};

export default function IncomeAnalysis() {
  const { country, bank } = useLocalSearchParams<{ country: string; bank: string }>();
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [incomeData, setIncomeData] = useState<IncomeData | null>(null);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportSubmitted, setReportSubmitted] = useState(false);
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);

  // Mock analysis process
  useEffect(() => {
    const analyzeIncome = async () => {
      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Mock income data with multiple accounts
      const mockAccounts: BankAccount[] = [
        {
          id: '1',
          bankName: bank || 'Chase Bank',
          iban: '**********************',
          monthlyIncome: 5000,
          averageIncome: 4850,
          incomeStability: 95,
          lastPayment: '2024-01-15',
          paymentFrequency: 'Bi-weekly'
        }
      ];

      setIncomeData({
        accounts: mockAccounts,
        totalMonthlyIncome: mockAccounts.reduce((sum, acc) => sum + acc.monthlyIncome, 0),
        totalAverageIncome: mockAccounts.reduce((sum, acc) => sum + acc.averageIncome, 0),
        overallStability: Math.round(mockAccounts.reduce((sum, acc) => sum + acc.incomeStability, 0) / mockAccounts.length)
      });

      setIsAnalyzing(false);
    };

    analyzeIncome();
  }, [bank]);

  const handleContinue = async () => {
    if (!isConfirmed) return;

    setIsLoading(true);

    try {
      router.push({
        pathname: '/(auth)/income-verification/banking/score-card',
        params: {
          country,
          bank,
          monthlyIncome: incomeData?.totalMonthlyIncome?.toString() || '0',
          averageIncome: incomeData?.totalAverageIncome?.toString() || '0',
          incomeStability: incomeData?.overallStability?.toString() || '0'
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleAddAnotherAccount = () => {
    // Navigate to standalone income verification method selection while preserving existing data
    router.push('/(auth)/income-verification');
  };

  const handleReportIncorrectData = () => {
    setShowReportModal(true);
  };

  const handleSubmitReport = () => {
    // Log the report for development purposes
    console.log('Income data issue reported for manual review');

    // Close modal and show success feedback
    setShowReportModal(false);
    setReportSubmitted(true);
    setShowSuccessBanner(true);

    // Hide success banner after 5 seconds
    setTimeout(() => {
      setShowSuccessBanner(false);
    }, 5000);
  };

  const handleCancelReport = () => {
    setShowReportModal(false);
  };

  const canProceed = (): boolean => {
    return !isAnalyzing && isConfirmed;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (isAnalyzing) {
    return (
      <StyledSafeAreaView className="flex-1 bg-white">
        <StatusBar style="dark" />

        <StyledScrollView className="flex-grow px-6 pb-10">
          <StyledView className="w-full max-w-md mx-auto flex-1">
            {/* Back button */}
            <StyledView className="mb-6 mt-4">
              <StyledTouchableOpacity
                onPress={handleBack}
                className="p-2 self-start"
              >
                <Feather name="arrow-left" size={24} color="#374151" />
              </StyledTouchableOpacity>
            </StyledView>

          {/* Loading Content */}
          <StyledView className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" color="#4ca2f5" />
            <StyledText className="text-2xl font-bold text-gray-900 mt-6 mb-4 text-center">
              Analyzing Your Income
            </StyledText>
            <StyledText className="text-lg text-gray-600 text-center leading-relaxed mb-8">
              We're securely analyzing 3 months of bank statement data to verify your income
            </StyledText>

            {/* Progress Steps */}
            <StyledView className="w-full max-w-sm">
              <StyledView className="flex-row items-center mb-3">
                <Feather name="check-circle" size={20} color="#10b981" />
                <StyledText className="text-green-700 ml-3">Connected to bank account</StyledText>
              </StyledView>
              <StyledView className="flex-row items-center mb-3">
                <ActivityIndicator size="small" color="#4ca2f5" />
                <StyledText className="text-blue-600 ml-3">Analyzing transaction data...</StyledText>
              </StyledView>
              <StyledView className="flex-row items-center">
                <StyledView className="w-5 h-5 border-2 border-gray-300 rounded-full mr-3" />
                <StyledText className="text-gray-500 ml-3">Generating income summary</StyledText>
              </StyledView>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}

return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1">
            <StyledView className="mb-12">
              <HeaderText>Income Summary</HeaderText>
              <SubtitleText>Here's what we found from your bank statements</SubtitleText>
            </StyledView>

            {/* Success Banner */}
            {showSuccessBanner && (
              <StyledView className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                <StyledView className="flex-row items-start">
                  <Feather name="check-circle" size={20} color="#10b981" />
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-sm font-medium text-green-900 mb-1">
                      Review Request Submitted
                    </StyledText>
                    <StyledText className="text-sm text-green-700">
                      Income review request submitted successfully. Our support team will review your information within 1-2 business days.
                    </StyledText>
                  </StyledView>
                </StyledView>
              </StyledView>
            )}

            {/* Connected Accounts */}
            <StyledView className="mb-8">
              <StyledText className="text-lg font-semibold text-gray-900 mb-4">Connected Bank Accounts</StyledText>

              {incomeData?.accounts.map((account) => (
                <StyledView key={account.id} className="bg-white border border-gray-200 rounded-xl p-6 mb-4 shadow-sm">
                  <StyledView className="mb-4">
                    <StyledText className="text-base font-semibold text-gray-900">{account.bankName}</StyledText>
                    <StyledText className="text-sm text-gray-500">{maskIban(account.iban)}</StyledText>
                  </StyledView>

                  <StyledView className="flex-row justify-between">
                    <StyledText className="text-sm text-gray-600">3-Month Average</StyledText>
                    <StyledText className="text-sm font-semibold text-gray-900">{formatCurrency(account.averageIncome)}</StyledText>
                  </StyledView>
                </StyledView>
              ))}
            </StyledView>

            {/* Report Incorrect Income Data Button */}
            <StyledView className="mb-6">
              <StyledTouchableOpacity
                className="bg-white border-2 border-secondary rounded-xl py-3 px-6 items-center shadow-sm"
                onPress={handleReportIncorrectData}
                disabled={isLoading || reportSubmitted}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="alert-triangle" size={16} color="#4ca2f5" />
                  <StyledText className={`text-sm font-semibold ml-2 ${
                    reportSubmitted ? 'text-gray-400' : 'text-secondary'
                  }`}>
                    {reportSubmitted ? 'Review Request Submitted' : 'Report Incorrect Income Data'}
                  </StyledText>
                </StyledView>
              </StyledTouchableOpacity>
            </StyledView>

            {/* Add Another Bank Account Button */}
            <StyledView className="mb-8">
              <StyledTouchableOpacity
                className="bg-white border-2 border-secondary rounded-xl py-4 px-6 items-center shadow-sm"
                onPress={handleAddAnotherAccount}
                disabled={isLoading}
              >
                <StyledView className="flex-row items-center">
                  <Feather name="plus" size={20} color="#4ca2f5" />
                  <StyledText className="text-base font-semibold text-secondary ml-2">
                    Add Another Bank Account
                  </StyledText>
                </StyledView>
                <StyledText className="text-sm text-gray-600 mt-1 text-center">
                  Connect multiple accounts for complete income verification
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>

            {/* Confirmation Checkbox */}
            <StyledView className="mb-8 pt-6 border-t border-gray-200">
              <StyledTouchableOpacity
                className="w-full py-3 items-center flex-row"
                onPress={() => setIsConfirmed(!isConfirmed)}
                disabled={isLoading}
                style={{ minHeight: 44 }}
              >
                <StyledView
                  className={`w-5 h-5 border-2 rounded mr-3 items-center justify-center ${
                    isConfirmed
                      ? 'border-secondary bg-secondary'
                      : 'border-gray-300 bg-white'
                  }`}
                >
                  {isConfirmed && (
                    <Feather name="check" size={14} color="white" />
                  )}
                </StyledView>
                <StyledText className="text-gray-600 text-base flex-1">
                  I confirm this income information is accurate
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Continue
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>

      {/* Report Issue Modal */}
      <Modal
        visible={showReportModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelReport}
      >
        <StyledView className="flex-1 bg-black/50 items-center justify-center p-6">
          <StyledView className="bg-white rounded-xl p-6 w-full max-w-sm mx-auto shadow-xl">
            {/* Modal Header */}
            <StyledView className="mb-4">
              <StyledText className="text-xl font-bold text-gray-900 mb-2">
                Report Income Data Issue
              </StyledText>
              <StyledText className="text-sm text-gray-600 leading-relaxed">
                Our support team will manually review your income information within 1-2 business days. You'll receive an email update once the review is complete.
              </StyledText>
            </StyledView>

            {/* Modal Actions */}
            <StyledView className="space-y-3">
              {/* Submit Report Button */}
              <StyledTouchableOpacity
                className="bg-secondary rounded-xl py-4 px-6 items-center shadow-sm"
                onPress={handleSubmitReport}
              >
                <StyledText className="text-white text-base font-semibold">
                  Submit Report
                </StyledText>
              </StyledTouchableOpacity>

              {/* Cancel Button */}
              <StyledTouchableOpacity
                className="bg-white border-2 border-gray-300 rounded-xl py-4 px-6 items-center"
                onPress={handleCancelReport}
              >
                <StyledText className="text-gray-700 text-base font-semibold">
                  Cancel
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        </StyledView>
      </Modal>
    </StyledSafeAreaView>
  );
}
