import React, { useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

const BANKS_BY_COUNTRY: { [key: string]: Array<{ id: string; name: string; logo: string }> } = {
  US: [
    { id: 'chase', name: 'Chase Bank', logo: '🏦' },
    { id: 'bofa', name: 'Bank of America', logo: '🏛️' },
    { id: 'wells', name: 'Wells Fargo', logo: '🏪' },
    { id: 'citi', name: 'Citibank', logo: '🏢' },
    { id: 'usbank', name: 'U.S. Bank', logo: '🏦' },
    { id: 'pnc', name: 'PNC Bank', logo: '🏛️' },
  ],
  UK: [
    { id: 'hsbc', name: 'HSBC', logo: '🏦' },
    { id: 'barclays', name: 'Barclays', logo: '🏛️' },
    { id: 'lloyds', name: 'Lloyds Bank', logo: '🏪' },
    { id: 'natwest', name: 'NatWest', logo: '🏢' },
    { id: 'santander', name: 'Santander UK', logo: '🏦' },
  ],
  CA: [
    { id: 'rbc', name: 'Royal Bank of Canada', logo: '🏦' },
    { id: 'td', name: 'TD Canada Trust', logo: '🏛️' },
    { id: 'scotiabank', name: 'Scotiabank', logo: '🏪' },
    { id: 'bmo', name: 'Bank of Montreal', logo: '🏢' },
  ],
  // Add more countries as needed
};

export default function BankSelection() {
  const { country } = useLocalSearchParams<{ country: string }>();
  const [selectedBank, setSelectedBank] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const banks = BANKS_BY_COUNTRY[country || 'US'] || BANKS_BY_COUNTRY.US;

  const handleContinue = async () => {
    if (!selectedBank) return;

    setIsLoading(true);

    try {
      router.push({
        pathname: '/(auth)/income-verification/banking/bank-login',
        params: { country, bank: selectedBank }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const canProceed = (): boolean => {
    return !!selectedBank;
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Select Your Bank</HeaderText>
              <SubtitleText>Choose your bank to connect your account securely</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {banks.map((bank) => (
                <StyledTouchableOpacity
                  key={bank.id}
                  className={`w-full border rounded-xl p-4 mb-3 transition-colors ${
                    selectedBank === bank.id
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => setSelectedBank(bank.id)}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledView className="flex-row items-center">
                      <StyledText className="text-2xl mr-3">{bank.logo}</StyledText>
                      <StyledText className={`text-base ${
                        selectedBank === bank.id ? 'text-secondary font-medium' : 'text-gray-900'
                      }`}>
                        {bank.name}
                      </StyledText>
                    </StyledView>
                    {selectedBank === bank.id && (
                      <Feather name="check" size={20} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
            </StyledView>

            {/* Security Notice */}
            <StyledView className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-8">
              <StyledView className="flex-row items-start">
                <Feather name="shield" size={20} color="#4ca2f5" />
                <StyledView className="ml-3 flex-1">
                  <StyledText className="text-sm font-medium text-blue-900 mb-1">
                    Bank-Grade Security
                  </StyledText>
                  <StyledText className="text-sm text-blue-700">
                    Your banking credentials are encrypted and never stored. We only access read-only transaction data.
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>
          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Continue
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
