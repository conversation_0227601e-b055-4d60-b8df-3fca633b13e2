import React, { useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

export default function BankLogin() {
  const { country, bank } = useLocalSearchParams<{ country: string; bank: string }>();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasGrantedAccess, setHasGrantedAccess] = useState(false);

  const handleGrantAccess = async () => {
    setIsLoading(true);

    try {
      // Mock authentication delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      setHasGrantedAccess(true);

      // Navigate to income analysis
      router.push({
        pathname: '/(auth)/income-verification/banking/income-analysis',
        params: { country, bank }
      });
    } catch (error) {
      console.error('Mock authentication error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const canProceed = (): boolean => {
    return username.trim().length > 0 && password.trim().length > 0;
  };

  const getBankName = (bankId: string): string => {
    const bankNames: { [key: string]: string } = {
      chase: 'Chase Bank',
      bofa: 'Bank of America',
      wells: 'Wells Fargo',
      citi: 'Citibank',
      hsbc: 'HSBC',
      barclays: 'Barclays',
      // Add more as needed
    };
    return bankNames[bankId] || 'Your Bank';
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Connect to {getBankName(bank || '')}</HeaderText>
              <SubtitleText>Enter your banking credentials to securely connect your account</SubtitleText>
            </StyledView>

            {/* Mock Bank Login Form */}
            <StyledView className="mb-8">
              {/* Security Notice */}
              <StyledView className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                <StyledView className="flex-row items-start">
                  <Feather name="lock" size={20} color="#10b981" />
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-sm font-medium text-green-900 mb-1">
                      Demo Mode - Safe to Use
                    </StyledText>
                    <StyledText className="text-sm text-green-700">
                      This is a demonstration. Your credentials are not stored or transmitted.
                    </StyledText>
                  </StyledView>
                </StyledView>
              </StyledView>

              {/* Username Field */}
              <StyledTextInput
                label="Username or Email"
                value={username}
                onChangeText={setUsername}
                placeholder="Enter your username"
                autoCapitalize="none"
                autoCorrect={false}
                disabled={isLoading}
              />

              {/* Password Field */}
              <StyledTextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                placeholder="Enter your password"
                secureTextEntry
                disabled={isLoading}
              />

              {/* Permissions Notice */}
              <StyledView className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <StyledText className="text-sm font-medium text-blue-900 mb-2">
                  Read-Only Access Requested
                </StyledText>
                <StyledText className="text-sm text-blue-700 mb-2">
                  CasaPay will access:
                </StyledText>
                <StyledText className="text-sm text-blue-700">• Account balances (last 3 months)</StyledText>
                <StyledText className="text-sm text-blue-700">• Transaction history (income verification)</StyledText>
                <StyledText className="text-sm text-blue-700">• Account details (verification only)</StyledText>
              </StyledView>
            </StyledView>
          </StyledView>

          {/* Grant Access Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleGrantAccess}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <StyledView className="flex-row items-center">
                <ActivityIndicator color="white" size="small" />
                <StyledText className="text-white text-base font-semibold ml-2">
                  Connecting...
                </StyledText>
              </StyledView>
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Grant Read-Only Access
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
