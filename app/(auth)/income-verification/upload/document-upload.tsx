import React, { useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { DocumentUpload } from '@/components/ui/DocumentUpload';
import { Feather } from '@expo/vector-icons';

interface DocumentUploadResult {
  uri: string;
  name: string;
  size: number;
  type: string;
}

export default function DocumentUploadScreen() {
  const [uploadedDocuments, setUploadedDocuments] = useState<DocumentUploadResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleDocumentUpload = (documents: DocumentUploadResult[]) => {
    setUploadedDocuments(documents);
  };

  const handleContinue = async () => {
    if (uploadedDocuments.length === 0) return;

    setIsLoading(true);

    try {
      router.push({
        pathname: '/(auth)/income-verification/upload/document-analysis',
        params: {
          documentCount: uploadedDocuments.length.toString(),
          documentNames: uploadedDocuments.map(doc => doc.name).join(',')
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const canProceed = (): boolean => {
    return uploadedDocuments.length > 0;
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Upload Bank Statements</HeaderText>
              <SubtitleText>Upload your last 3 months of bank statements for income verification</SubtitleText>
            </StyledView>

            {/* Document Upload Component */}
            <StyledView className="mb-8">
              <DocumentUpload
                onUpload={handleDocumentUpload}
                title="Upload Bank Statements"
                subtitle="Select PDF files of your bank statements from the last 3 months"
                acceptedTypes={['application/pdf']}
                maxFiles={3}
                disabled={isLoading}
              />
            </StyledView>

            {/* Requirements */}
            <StyledView className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-8">
              <StyledText className="text-sm font-medium text-blue-900 mb-3">
                Document Requirements:
              </StyledText>
              <StyledView className="space-y-2">
                <StyledView className="flex-row items-start">
                  <StyledText className="text-blue-700 mr-2">•</StyledText>
                  <StyledText className="text-sm text-blue-700 flex-1">
                    Bank statements from the last 3 months
                  </StyledText>
                </StyledView>
                <StyledView className="flex-row items-start">
                  <StyledText className="text-blue-700 mr-2">•</StyledText>
                  <StyledText className="text-sm text-blue-700 flex-1">
                    PDF format only (up to 10MB each)
                  </StyledText>
                </StyledView>
                <StyledView className="flex-row items-start">
                  <StyledText className="text-blue-700 mr-2">•</StyledText>
                  <StyledText className="text-sm text-blue-700 flex-1">
                    Clear, readable documents showing income deposits
                  </StyledText>
                </StyledView>
                <StyledView className="flex-row items-start">
                  <StyledText className="text-blue-700 mr-2">•</StyledText>
                  <StyledText className="text-sm text-blue-700 flex-1">
                    Official bank statements (not screenshots)
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Security Notice */}
            <StyledView className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8">
              <StyledView className="flex-row items-start">
                <Feather name="shield" size={20} color="#10b981" />
                <StyledView className="ml-3 flex-1">
                  <StyledText className="text-sm font-medium text-green-900 mb-1">
                    Secure Document Handling
                  </StyledText>
                  <StyledText className="text-sm text-green-700">
                    Your documents are encrypted and securely processed. We only extract income information and delete files after verification.
                  </StyledText>
                </StyledView>
              </StyledView>
            </StyledView>

            {/* Processing Time Notice */}
            {uploadedDocuments.length > 0 && (
              <StyledView className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-8">
                <StyledView className="flex-row items-start">
                  <Feather name="clock" size={20} color="#f59e0b" />
                  <StyledView className="ml-3 flex-1">
                    <StyledText className="text-sm font-medium text-yellow-900 mb-1">
                      Processing Time
                    </StyledText>
                    <StyledText className="text-sm text-yellow-700">
                      Document verification typically takes 1-2 business days. You'll receive an email when complete.
                    </StyledText>
                  </StyledView>
                </StyledView>
              </StyledView>
            )}
          </StyledView>

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceed()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceed()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceed() ? 'text-gray-500' : 'text-white'
              }`}>
                Continue
              </StyledText>
            )}
          </StyledTouchableOpacity>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
