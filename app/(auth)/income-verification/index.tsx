import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { StyledText, StyledView, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

export default function IncomeVerificationMethodSelection() {
  const [selectedMethod, setSelectedMethod] = useState<'banking' | 'document' | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleMethodSelection = async (method: 'banking' | 'document') => {
    setSelectedMethod(method);
    setIsLoading(true);

    try {
      if (method === 'banking') {
        router.push('/(auth)/income-verification/banking/country-selection');
      } else {
        router.push('/(auth)/income-verification/upload/document-upload');
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleSkip = () => {
    // Navigate back to wherever this was called from
    router.back();
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <StyledTouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </StyledTouchableOpacity>
          </StyledView>

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Income Verification</HeaderText>
              <SubtitleText>Verify your income for better offers (optional)</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {/* Verification Options */}
              <StyledView className="space-y-4">
                {/* Open Banking Option */}
                <StyledTouchableOpacity
                  className={`w-full border rounded-xl p-6 transition-colors ${
                    selectedMethod === 'banking'
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => handleMethodSelection('banking')}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledView className="flex-1">
                      <StyledView className="flex-row items-center mb-3">
                        <Feather name="link" size={24} color="#4ca2f5" />
                        <StyledText className="text-lg font-semibold text-gray-900 ml-3">
                          Connect Your Bank
                        </StyledText>
                      </StyledView>
                      <StyledText className="text-gray-600 mb-3">
                        Securely connect your bank account to receive an instant verification
                      </StyledText>
                      <StyledView className="space-y-1">
                        <StyledText className="text-gray-700 text-sm">• Instant verification</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Bank-grade security</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Better loan offers</StyledText>
                      </StyledView>
                    </StyledView>
                    {selectedMethod === 'banking' && (
                      <Feather name="check" size={24} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>

                {/* Document Upload Option */}
                <StyledTouchableOpacity
                  className={`w-full border rounded-xl p-6 transition-colors ${
                    selectedMethod === 'document'
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => handleMethodSelection('document')}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledView className="flex-1">
                      <StyledView className="flex-row items-center mb-3">
                        <Feather name="upload" size={24} color="#4ca2f5" />
                        <StyledText className="text-lg font-semibold text-gray-900 ml-3">
                          Upload Documents
                        </StyledText>
                      </StyledView>
                      <StyledText className="text-gray-600 mb-3">
                        Upload pay stubs or tax documents for verification
                      </StyledText>
                      <StyledView className="space-y-1">
                        <StyledText className="text-gray-700 text-sm">• Manual review process</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Secure document handling</StyledText>
                        <StyledText className="text-gray-700 text-sm">• 1-2 business days</StyledText>
                      </StyledView>
                    </StyledView>
                    {selectedMethod === 'document' && (
                      <Feather name="check" size={24} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              </StyledView>
            </StyledView>

            {/* Skip Option */}
            <StyledView className="mb-8">
              <StyledTouchableOpacity
                className="w-full py-3 items-center flex-row justify-center"
                onPress={handleSkip}
                disabled={isLoading}
                style={{ minHeight: 44 }}
              >
                <StyledText className="text-gray-600 text-base">
                  Skip for now - I'll complete this later
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
