import React, { useState, useEffect } from 'react';
import { ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { Logo } from '@/components/ui/Logo';

export default function ForgotPasswordScreen() {
  const { state, forgotPassword, clearError } = useAuth();

  const [email, setEmail] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Clear any errors when component mounts
  useEffect(() => {
    clearError();
  }, []);

  const validateForm = () => {
    if (!email.trim()) {
      setFormError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setFormError('Email is invalid');
      return false;
    }

    setFormError(null);
    return true;
  };

  const handleForgotPassword = async () => {
    // Form validation
    if (!validateForm()) {
      return;
    }

    // Call forgotPassword from auth context
    await forgotPassword(email);
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />
      {/* Logo and Header - Moved outside ScrollView */}
      <StyledView className="items-center pt-2">
        <Logo size="large" />
        <StyledText className="text-base text-dark-gray text-center">Tenant Portal</StyledText>
      </StyledView>

      <StyledScrollView className="flex-grow px-6 pb-10">
        {/* Removed Logo and Header from here */}

        <StyledView className="items-center mb-6 mt-4">
          <StyledText className="text-2xl font-semibold mb-2 text-center text-primary">Reset Password</StyledText>
          <StyledText className="text-base text-dark-gray mb-2 text-center">
            Enter your email address and we'll send you instructions to reset your password
          </StyledText>
        </StyledView>

        <StyledView className="w-full max-w-md mx-auto">
            {/* Error message from auth context */}
            {state.error ? (
              <StyledView className="mb-6 py-3 px-5 bg-danger-accent rounded-md">
                <StyledText className="text-danger text-sm">{state.error}</StyledText>
              </StyledView>
            ) : null}

            {/* Form validation error */}
            {formError ? (
              <StyledView className="mb-6 py-3 px-5 bg-danger-accent rounded-md">
                <StyledText className="text-danger text-sm">{formError}</StyledText>
              </StyledView>
            ) : null}

            {/* Email Field */}
            <StyledView className="mb-8 w-full">
              <StyledText className="text-sm font-medium mb-2 text-dark-gray">Email address</StyledText>
              <StyledTextInput
                className="w-full"
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!state.isLoading}
              />
            </StyledView>

            {/* Submit Button */}
            <StyledTouchableOpacity
              className={`bg-secondary rounded-xl py-4 items-center w-full shadow-button-secondary ${state.isLoading ? 'opacity-70' : ''}`}
              onPress={handleForgotPassword}
              disabled={state.isLoading}
            >
              {state.isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <StyledText className="text-white text-base font-medium">Send Reset Instructions</StyledText>
              )}
            </StyledTouchableOpacity>

            {/* Back to Log In Link */}
            <StyledTouchableOpacity
              className="items-center mt-8"
              onPress={() => router.push('/(auth)/login')}
            >
              <StyledText className="text-link text-sm">Back to Log In</StyledText>
            </StyledTouchableOpacity>
          </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
