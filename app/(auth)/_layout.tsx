import React from 'react';
import { Stack } from 'expo-router';
import { View, Platform, StatusBar } from 'react-native';
import { Logo } from '@/components/ui/Logo';

export default function AuthLayout() {
  const statusBarHeight = Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 0;
  
  return (
    <View className="flex-1 bg-white">
      <View 
        className="px-5 py-2 bg-white border-b border-gray-200 flex-row items-center"
        style={{ paddingTop: statusBarHeight + 8 }}
      >
        <Logo size="small" showText={false} />
      </View>
      <Stack screenOptions={{ headerShown: false }} />
    </View>
  );
}
