import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput, ActivityIndicator, Alert, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { apiRequest } from '@/utils/apiUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function CreateAccountScreen() {
  const { invitation } = useLocalSearchParams<{ invitation: string }>();
  const { state, clearError, signup } = useAuth();

  const [password, setPassword] = useState('');
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Clear any errors when component mounts
  useEffect(() => {
    clearError();
    if (!invitation) {
      Alert.alert('Error', 'Invalid invitation link', [
        { text: 'OK', onPress: () => router.replace('/(auth)/login') }
      ]);
    }
  }, []);

  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    if (!password) {
      errors.password = 'Password is required';
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    if (password !== passwordConfirmation) {
      errors.passwordConfirmation = 'Passwords do not match';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateAccount = async () => {
    // Form validation
    if (!validateForm() || !invitation) {
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Use the signup function from AuthContext
      await signup({
        invitationHash: invitation,
        password,
        passwordConfirmation
      });

      // No need to handle navigation here as it's done in the signup function
    } catch (error) {
      // Display the error message
      setError(error instanceof Error ? error.message : 'Failed to create account. Please try again.');
      console.error('Error in handleCreateAccount:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <ScrollView className="flex-grow px-6 py-10">

        <View className="items-center mb-10">
          <Text className="text-2xl font-semibold mb-2 text-center text-secondary">Complete Your Account</Text>
          <Text className="text-base text-gray-600 mb-2 text-center">You've been invited to join CasaPay</Text>
          <Text className="text-sm text-gray-600 text-center">Please set a password to complete your registration</Text>
        </View>

        <View className="w-full max-w-md self-center">
          {/* Error message from auth context or local error state */}
          {(state.error || error) && (
            <View className="mb-6 p-3 px-5 bg-red-100 rounded-md">
              <Text className="text-red-600 text-sm">{state.error || error}</Text>
            </View>
          )}

          {/* Password Field */}
          <View className="mb-5 w-full">
            <Text className="form-label text-sm font-medium mb-2 text-gray-600">Password</Text>
            <TextInput
              className={`form-control w-full border rounded-md p-3 text-base ${formErrors.password ? 'border-red-600' : 'border-gray-300'}`}
              placeholder="Create a password"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (error || state.error) {
                  clearError();
                  setError(null);
                }
              }}
              secureTextEntry
              editable={!submitting}
            />
            {formErrors.password && (
              <Text className="text-red-600 text-xs mt-1">{formErrors.password}</Text>
            )}
          </View>

          {/* Password Confirmation Field */}
          <View className="mb-8 w-full">
            <Text className="form-label text-sm font-medium mb-2 text-gray-600">Confirm Password</Text>
            <TextInput
              className={`form-control w-full border rounded-md p-3 text-base ${formErrors.passwordConfirmation ? 'border-red-600' : 'border-gray-300'}`}
              placeholder="Confirm your password"
              value={passwordConfirmation}
              onChangeText={(text) => {
                setPasswordConfirmation(text);
                if (error || state.error) {
                  clearError();
                  setError(null);
                }
              }}
              secureTextEntry
              editable={!submitting}
            />
            {formErrors.passwordConfirmation && (
              <Text className="text-red-600 text-xs mt-1">{formErrors.passwordConfirmation}</Text>
            )}
          </View>

          {/* Create Account Button */}
          <TouchableOpacity
            className={`bg-secondary rounded-xl py-4 items-center w-full ${submitting ? 'opacity-70' : 'opacity-100'} shadow-md`}
            onPress={handleCreateAccount}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text className="text-white text-base font-medium">Create Account</Text>
            )}
          </TouchableOpacity>

        </View>
      </ScrollView>
    </SafeAreaView>
  );
}