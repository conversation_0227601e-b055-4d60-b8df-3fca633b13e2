import { Stack } from 'expo-router';
import React from 'react';
import { View, Platform, StatusBar } from 'react-native';
import { Logo } from '@/components/ui/Logo';

export default function WelcomeLayout() {
  const statusBarHeight = Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 0;
  
  return (
    <View className="flex-1 bg-white">
      <View 
        className="px-5 py-2 bg-white border-b border-gray-200 flex-row items-center"
        style={{ paddingTop: statusBarHeight + 8 }}
      >
        <Logo size="small" showText={false} />
      </View>
      <Stack
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right', // Smooth transitions between welcome screens
        }}
      >
        <Stack.Screen 
          name="index" 
          options={{ 
            headerShown: false,
            gestureEnabled: false, // Disable swipe back gesture for welcome flow
          }} 
        />
      </Stack>
    </View>
  );
}
