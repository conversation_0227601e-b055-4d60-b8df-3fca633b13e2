import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';

// This is a fallback component required by Expo Router when platform-specific files exist.
// Expo Router should prioritize index.web.tsx on web and index.native.tsx on native.
export default function WelcomeIndexFallback() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" />
      <Text>Loading Welcome Screen...</Text>
    </View>
  );
} 