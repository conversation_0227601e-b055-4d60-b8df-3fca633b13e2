import React, { useRef, useState, useEffect } from 'react';
import { View, SafeAreaView, Animated } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeScreenNative() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const pagerRef = useRef<PagerView>(null);

  // Create persistent animated values for each segment
  const progressAnimations = useRef(
    WELCOME_STEPS_DATA.map(() => new Animated.Value(0))
  ).current;

  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleGetStarted = async () => {
    // If not on the last step, advance to next page
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      const nextPage = currentPage + 1;
      pagerRef.current?.setPage(nextPage);
      return;
    }

    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const onPageSelected = (e: any) => {
    const newPage = e.nativeEvent.position;
    setCurrentPage(newPage);

    // Reset current page animation and start it
    progressAnimations[newPage].setValue(0);
    startProgressAnimation(newPage);
  };

  const startProgressAnimation = (pageIndex: number) => {
    // Clear existing timer
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      progressAnimations[pageIndex].setValue(1);
      return;
    }

    // Animate progress bar over 3 seconds
    Animated.timing(progressAnimations[pageIndex], {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        pagerRef.current?.setPage(nextPage);
      }
    }, 3000);
  };

  useEffect(() => {
    // Start the first page animation
    startProgressAnimation(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, []);

  const renderProgressIndicators = () => {
    return (
      <View className="flex-row justify-center mb-8">
        {WELCOME_STEPS_DATA.map((_, index) => (
          <View
            key={index}
            className={`w-3 h-3 rounded-full mx-1.5 transition-colors ${
              index <= currentPage
                ? 'bg-secondary'
                : 'bg-gray-300'
            }`}
          />
        ))}
      </View>
    );
  };

  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="px-6 py-4">
        {renderProgressIndicators()}
      </View>
      <PagerView
        ref={pagerRef}
        style={{ flex: 1 }}
        initialPage={0}
        onPageSelected={onPageSelected}
      >
        {WELCOME_STEPS_DATA.map((step) => (
          <View key={step.id} className="flex-1">
            <WelcomeStep
              imageSource={step.imageSource}
              onGetStarted={!isLastStep ? handleGetStarted : undefined}
              onLogin={isLastStep ? handleLogin : undefined}
              onRegister={isLastStep ? handleRegister : undefined}
              showDualButtons={isLastStep}
            />
          </View>
        ))}
      </PagerView>
    </SafeAreaView>
  );
}
