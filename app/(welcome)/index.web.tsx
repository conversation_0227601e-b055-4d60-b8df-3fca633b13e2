import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';
import { StyledView } from '../../components/ui/StyledComponents';

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeIndexWeb() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleGetStarted = async () => {
    // If not on the last step, advance to next page
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      const nextPage = currentPage + 1;
      goToPage(nextPage);
      return;
    }

    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const goToPage = (pageIndex: number) => {
    setCurrentPage(pageIndex);
    startAutoAdvance(pageIndex);
  };

  const startAutoAdvance = (pageIndex: number) => {
    // Clear existing timer
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      return;
    }

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        goToPage(nextPage);
      }
    }, 3000);
  };

  useEffect(() => {
    // Start the first page auto-advance
    startAutoAdvance(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, []);

  const renderProgressIndicators = () => {
    return (
      <StyledView className="flex-row justify-center mb-8">
        {WELCOME_STEPS_DATA.map((_, index) => (
          <StyledView
            key={index}
            className={`w-3 h-3 rounded-full mx-1.5 transition-colors ${
              index <= currentPage
                ? 'bg-secondary'
                : 'bg-gray-300'
            }`}
          />
        ))}
      </StyledView>
    );
  };

  const currentStep = WELCOME_STEPS_DATA[currentPage];
  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <StyledView className="flex-1 bg-white items-center justify-center min-h-screen">
      <StyledView className="w-full max-w-md mx-auto flex-1 px-6 py-8">
        {renderProgressIndicators()}
        <StyledView className="flex-1 justify-center">
          <WelcomeStep
            imageSource={currentStep.imageSource}
            onGetStarted={!isLastStep ? handleGetStarted : undefined}
            onLogin={isLastStep ? handleLogin : undefined}
            onRegister={isLastStep ? handleRegister : undefined}
            showDualButtons={isLastStep}
          />
        </StyledView>
      </StyledView>
    </StyledView>
  );
}
