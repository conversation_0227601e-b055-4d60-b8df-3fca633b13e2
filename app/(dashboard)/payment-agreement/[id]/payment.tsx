import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Linking, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { apiRequest } from '@/utils/apiUtils';
import type { OutstandingPayment, PaymentAgreement } from '@/types';
import { FontAwesome5 } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { formatCurrency } from '@/utils/i10n';

export default function PaymentScreen() {
  // State for data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [outstandingPayments, setOutstandingPayments] = useState<OutstandingPayment[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [plaidLinkToken, setPlaidLinkToken] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'everypay_bank' | 'everypay_card' | 'plaid' | null>(null);
  const [bankSelected, setBankSelected] = useState(false);
  const { id } = useLocalSearchParams();
  const [paymentAgreement, setPaymentAgreement] = useState<PaymentAgreement>();
  const [loadingPA, setLoadingPA] = useState(true);

  useEffect(() => {
    const fetchPA = async () => {
      try {
        setLoadingPA(true);

        // Mock payment agreement data for demo purposes
        const mockPaymentAgreement: PaymentAgreement = {
          id: 101,
          agreement_id: "PA-2024-001",
          status: "active",
          agreement_start: "2024-01-01",
          agreement_end: "2024-12-31",
          base_monthly_rent: "2500.00",
          base_monthly_fee_amount: "150.00",
          utilities_included: false,
          payout_day: "1",
          currency: "GBP",
          tenant_id: 1,
          property_id: 1,
          entity_id: 1,
          agreement_type: "standard",
          payout_model: "monthly",
          operator_fee_amount: "25.00",
          operator_fee_percent: "1.0",
          operator_share: "1.0",
          owner_share: "99.0",
          tenant_fee_amount: "0.00",
          tenant_fee_percent: "0.0",
          tenant_plan: "standard",
          vat_pc: "20.0",
          application_link: "",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          deleted_at: null,
          addons: [],
          one_time_fees: [],
          property: {
            id: 1,
            street_address: "10 Downing Street",
            city: "London",
            state: "",
            postal_code: "SW1A 2AA",
            country: "UK",
            property_type: "apartment",
            bedrooms: 3,
            bathrooms: 2,
            size: 120,
            size_unit: "sqm"
          },
          operator: {
            id: 1,
            name: "CasaPay Management",
            entity_type: "company",
            phone: "+44 20 7946 0958",
            email: "<EMAIL>",
            payment_methods: ["plaid", "everypay_bank", "everypay_card"]
          }
        };

        setPaymentAgreement(mockPaymentAgreement);
        setLoadingPA(false);
        return;

        // Original API call (commented out for mock)
        /*
        const response = await apiRequest(
          '/api/v1/tenant/mcp',
          'POST',
          {
            jsonrpc: '2.0',
            method: 'tenant.get_payment_agreement',
            params: { agreement_id: id },
          },
        );
        if (response && response.result) {
          setPaymentAgreement(response.result.payment_agreement);
        }
        */
      } catch (error) {
        console.error('Failed to fetch payment agreement:', error);
      } finally {
        setLoadingPA(false);
      }
    };
    if (id) fetchPA();
  }, [id]);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Make Payment - CasaPay';
    }
  }, []);

  // Fetch outstanding payments
  useEffect(() => {
    const fetchOutstandingPayments = async () => {
      try {
        setLoading(true);

        // Mock data for demo purposes
        const mockOutstandingPayments: OutstandingPayment[] = [
          {
            id: 1,
            total_amount: "2500.00",
            paid_amount: 0,
            remaining_amount: 2500.00,
            due_date: '2024-01-31',
            status: 'overdue',
            lines: [
              {
                description: 'Monthly Rent - January 2024',
                quantity: 1,
                unit_price: "2500.00",
                total_price: "2500.00"
              }
            ],
            selected: true
          },
          {
            id: 2,
            total_amount: "150.00",
            paid_amount: 0,
            remaining_amount: 150.00,
            due_date: '2024-01-31',
            status: 'due',
            lines: [
              {
                description: 'Utilities - January 2024',
                quantity: 1,
                unit_price: "150.00",
                total_price: "150.00"
              }
            ],
            selected: true
          },
          {
            id: 3,
            total_amount: "300.00",
            paid_amount: 0,
            remaining_amount: 300.00,
            due_date: '2024-03-31',
            status: 'upcoming',
            lines: [
              {
                description: 'Service Charge - Q1 2024',
                quantity: 1,
                unit_price: "300.00",
                total_price: "300.00"
              }
            ],
            selected: false
          }
        ];

        // Use mock data directly
        setOutstandingPayments(mockOutstandingPayments);
        calculateTotal(mockOutstandingPayments);
        setLoading(false);
        return;

        // Original API call (commented out for mock)
        /*
        const response = await apiRequest(
          '/api/v1/tenant/mcp',
          'POST',
          {
            jsonrpc: '2.0',
            method: 'tenant.get_outstanding_payments',
            params: {}
          },
        );

        if (response && response.result && response.result.outstanding_payments) {
          // Filter out payments with 0 remaining amount and add selected property to the rest
          const payments = response.result.outstanding_payments
            .filter((payment: any) => payment.remaining_amount > 0) // Only include payments with remaining amount > 0
            .map((payment: any) => ({
              ...payment,
              selected: true // Set all payments as selected by default
            }));

          setOutstandingPayments(payments);
          calculateTotal(payments);
        } else {
          setError('No outstanding payments found');
        }
        */
      } catch (err) {
        console.error('Error fetching outstanding payments:', err);
        setError('Failed to load outstanding payments');
      } finally {
        setLoading(false);
      }
    };

    fetchOutstandingPayments();
  }, []);

  // Calculate total amount based on selected payments
  const calculateTotal = (payments: OutstandingPayment[]) => {
    const total = payments
      .filter(payment => payment.selected)
      .reduce((sum, payment) => sum + payment.remaining_amount, 0);

    setTotalAmount(total);
  };

  // Toggle payment selection
  const togglePaymentSelection = (id: number) => {
    const updatedPayments = outstandingPayments.map(payment =>
      payment.id === id ? { ...payment, selected: !payment.selected } : payment
    );

    setOutstandingPayments(updatedPayments);
    calculateTotal(updatedPayments);
  };

  // Toggle bank selection without initiating payment
  const toggleBankSelection = () => {
    console.log('🏦 Toggling bank selection');
    const newValue = !bankSelected;
    setBankSelected(newValue);
  };

  // Initiate payment with Plaid - called when Pay button is pressed
  const initiatePayment = async () => {
    const selectedPaymentIds = outstandingPayments
      .filter(payment => payment.selected)
      .map(payment => payment.id);

    if (selectedPaymentIds.length === 0) {
      Alert.alert('No payments selected', 'Please select at least one payment to proceed.');
      return;
    }

    setSubmitting(true);

    // Mock Plaid payment flow
    try {
      console.log('🎭 Starting mock Plaid payment flow...');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate mock payment ID
      const mockPaymentId = `plaid-payment-${Date.now()}`;
      setPaymentId(mockPaymentId);

      console.log('✅ Mock Plaid payment initiated successfully with ID:', mockPaymentId);

      // Navigate directly to payment processing screen (skipping Plaid Link)
      router.replace({
        pathname: '/(dashboard)/payment-processing',
        params: {
          paymentId: mockPaymentId,
          amount: formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP')
        }
      });

    } catch (error) {
      console.error('Mock Plaid payment error:', error);
      Alert.alert('Payment Error', 'Something went wrong. Please try again.');
      setSubmitting(false);
    }

    // Original Plaid flow (commented out for mock)
    /*
    // Clear any previous payment ID from localStorage to avoid using stale data
    try {
      localStorage.removeItem('lastPlaidPaymentId');
    } catch (e) {
      console.warn('Could not clear previous payment ID from localStorage', e);
    }

    try {
      // Make API call to initiate payment
      const response = await apiRequest(
        '/api/v1/tenant/mcp',
        'POST',
        {
          jsonrpc: '2.0',
          method: 'tenant.initiate_payment',
          params: {
            invoice_ids: selectedPaymentIds
          }
        },
      );

      if (response && response.result) {
        // Store the payment ID - IMPORTANT: use the exact ID from the backend
        const backendPaymentId = response.result.payment_id;
        console.log('💳 Payment ID from backend:', backendPaymentId);

        // Store in localStorage immediately
        try {
          localStorage.setItem('lastPlaidPaymentId', backendPaymentId);
          console.log('💾 Payment ID stored in localStorage immediately after receiving');
        } catch (e) {
          console.warn('Could not store payment ID in localStorage', e);
        }

        // Update state (this is async and won't be available immediately)
        setPaymentId(backendPaymentId);

        // Request a link token from Plaid
        const linkTokenResponse = await apiRequest(
          '/api/v1/tenant/mcp',
          'POST',
          {
            jsonrpc: '2.0',
            method: 'tenant.get_plaid_link_token',
            params: {
              payment_id: backendPaymentId
            }
          },
        );

        if (linkTokenResponse && linkTokenResponse.result && linkTokenResponse.result.link_token) {
          const token = linkTokenResponse.result.link_token;
          console.log('✅ Received Plaid link token from API');
          setPlaidLinkToken(token);
          // Open Plaid Link when token is received
          console.log('⏱️ Waiting 1 second before opening Plaid...');
          setTimeout(() => openPlaidLink(token, backendPaymentId), 1000);
        } else {
          console.error('❌ No link token received from API');
          setSubmitting(false);
          Alert.alert('Payment Error', 'Failed to get Plaid link token from API');
        }
      } else {
        throw new Error('Failed to initiate payment');
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Payment Error', `Failed to initiate payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setSubmitting(false);
    }
    */
  };

  // Handle EveryPay payment
  const handleEveryPayPayment = async () => {
    if(selectedPaymentMethod === 'plaid') {
      return initiatePayment();
    }
    const selectedInvoiceIds = outstandingPayments
      .filter(payment => payment.selected)
      .map(payment => payment.id);

    if (selectedInvoiceIds.length === 0) {
      Alert.alert('No invoices selected', 'Please select at least one invoice to proceed.');
      return;
    }
    if (!selectedPaymentMethod) {
      Alert.alert('No payment method selected', 'Please select a payment method to proceed.');
      return;
    }

    setSubmitting(true);

    // Mock payment flow - simulate processing time and success
    try {
      console.log('🎭 Starting mock payment flow...');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate mock payment ID
      const mockPaymentId = `mock-payment-${Date.now()}`;
      setPaymentId(mockPaymentId);

      console.log('✅ Mock payment initiated successfully with ID:', mockPaymentId);

      // Navigate to payment processing screen
      router.replace({
        pathname: '/(dashboard)/payment-processing',
        params: {
          paymentId: mockPaymentId,
          amount: formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP')
        }
      });

    } catch (error) {
      console.error('Mock payment error:', error);
      Alert.alert('Payment Error', 'Something went wrong. Please try again.');
      setSubmitting(false);
    }

    // Original API code (commented out for mock)
    /*
    try {
      // Dynamically get base URL for success/abort URLs
      let baseUrl = '';
      if (typeof window !== 'undefined' && window.location && window.location.origin) {
        baseUrl = window.location.origin;
      } else {
        // fallback for non-web environments
        baseUrl = process.env.BASE_URL || '';
      }
      const response = await apiRequest(
        '/api/v1/tenant/mcp',
        'POST',
        {
          jsonrpc: '2.0',
          method: 'tenant.initiate_payment',
          params: {
            payment_method: selectedPaymentMethod,
            invoice_ids: selectedInvoiceIds,
            success_url: `${baseUrl}/(dashboard)/payment-success`,
            abort_url: `${baseUrl}/(dashboard)`
          },
        },
      );

      if (response && response.result) {
        const paymentUrl = response.result.payment_url;
        if (paymentUrl) {
          // Open EveryPay payment URL in the current tab (web) or browser (React Native)
          if (typeof window !== 'undefined' && window.location) {
            window.location.href = paymentUrl;
          } else {
            // For React Native (Expo)
            try {
              Linking.openURL(paymentUrl);
            } catch (e) {
              console.error('Unable to open payment URL:', e);
              Alert.alert('Payment URL', paymentUrl);
            }
          }
        } else {
          Alert.alert('Success', 'EveryPay payment initiated, but no payment URL returned.');
        }
      } else {
        throw new Error(response?.error?.message || 'Failed to initiate EveryPay payment');
      }
    } catch (error) {
      console.error('EveryPay payment error:', error);
      Alert.alert('Payment Error', `Failed to initiate EveryPay payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setSubmitting(false);
    }
    */
  };

  // Open Plaid link using the official Plaid Link JavaScript
  const openPlaidLink = (token?: string, directPaymentId?: string) => {
    // Use the passed token or fall back to the state
    const linkToken = token || plaidLinkToken;

    if (!linkToken) {
      console.error('❌ No Plaid link token available!');
      return;
    }

    console.log('🔗 Opening Plaid with token:', linkToken);

    // Load the Plaid Link script if it's not already loaded
    if (!document.getElementById('plaid-link-script')) {
      const script = document.createElement('script');
      script.id = 'plaid-link-script';
      script.src = 'https://cdn.plaid.com/link/v2/stable/link-initialize.js';
      script.async = true;
      script.onload = () => {
        console.log('✅ Plaid Link script loaded');
        initializePlaidLink(linkToken, directPaymentId);
      };
      document.head.appendChild(script);
    } else {
      // Script already loaded, initialize Plaid Link directly
      initializePlaidLink(linkToken, directPaymentId);
    }
  };

  // Initialize Plaid Link with the token
  const initializePlaidLink = (token: string, directPaymentId?: string) => {
    // Use directly passed payment ID (from initiatePayment) or fall back to state
    const currentPaymentId = directPaymentId || paymentId;

    // Store payment ID in localStorage immediately when initializing Plaid
    // This ensures it's available regardless of which events fire
    if (currentPaymentId) {
      try {
        console.log('💾 Storing payment ID in localStorage before opening Plaid:', currentPaymentId);
        localStorage.setItem('lastPlaidPaymentId', currentPaymentId);
      } catch (e) {
        console.warn('Could not store payment ID in localStorage', e);
      }
    } else {
      console.warn('⚠️ No payment ID available when initializing Plaid');
    }

    // @ts-ignore - Plaid is loaded via script
    const handler = window.Plaid.create({
      token: token,
      onSuccess: (public_token: string, metadata: any) => {
        console.log('✅ Plaid Link Success:', metadata);
        // Navigate to payment processing screen instead of directly to success
        navigateToProcessing();
      },
      onExit: (err: any, metadata: any) => {
        console.log('❌ Plaid Link Exit:', err, metadata);
        setSubmitting(false);
      },
      onEvent: (eventName: string, metadata: any) => {
        console.log('💬 Plaid Link Event:', eventName, metadata);

        // Handle various events to ensure payment ID is stored
        if (['HANDOFF', 'SELECT_INSTITUTION', 'OPEN_OAUTH'].includes(eventName)) {
          // First try to use the direct payment ID, then fall back to state
          const currentPaymentId = directPaymentId || paymentId;

          if (currentPaymentId) {
            console.log(`🔗 ${eventName} event detected with payment ID:`, currentPaymentId);
            // Store the payment ID in localStorage as a backup
            try {
              localStorage.setItem('lastPlaidPaymentId', currentPaymentId);
              console.log('💾 Payment ID stored in localStorage for recovery');
            } catch (e) {
              console.warn('Could not store payment ID in localStorage', e);
            }
          } else {
            console.warn(`⚠️ No payment ID available during ${eventName} event`);
          }
        }
      },
    });

    // Open Plaid Link
    handler.open();
  };

  // Navigate to the payment processing screen
  const navigateToProcessing = () => {
    // Try to get payment ID from state or localStorage as fallback
    let currentPaymentId = paymentId;

    if (!currentPaymentId) {
      console.warn('⚠️ No payment ID in state, trying to recover from localStorage');
      try {
        const storedId = localStorage.getItem('lastPlaidPaymentId');
        if (storedId) {
          console.log('💾 Recovered payment ID from localStorage:', storedId);
          currentPaymentId = storedId;
          // Update state with recovered ID
          setPaymentId(storedId);
        }
      } catch (e) {
        console.error('Could not access localStorage', e);
      }
    }

    // Additional fallback: check URL parameters
    if (!currentPaymentId) {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const urlPaymentId = urlParams.get('paymentId');
        if (urlPaymentId) {
          console.log('💾 Recovered payment ID from URL parameter:', urlPaymentId);
          currentPaymentId = urlPaymentId;
          setPaymentId(urlPaymentId);
        }
      } catch (e) {
        console.error('Could not access URL parameters', e);
      }
    }

    if (!currentPaymentId) {
      console.error('❌ No payment ID available for processing');
      Alert.alert('Error', 'Payment ID is missing. Please try again.');
      setSubmitting(false);
      return;
    }

    console.log('🚀 Navigating to payment processing with ID:', currentPaymentId);

    router.replace({
      pathname: '/(dashboard)/payment-processing',
      params: {
        paymentId: currentPaymentId,
        amount: formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP')
      }
    });
  };

  // No need for message event listeners with the official Plaid Link approach

  // Handle successful payment completion
  const handleSuccessfulPayment = () => {
    // Navigate to success screen
    router.replace({
      pathname: '/(dashboard)/payment-success',
      params: {
        transactionId: paymentId || 'plaid-' + Date.now(),
        amount: formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP'),
        property: 'Multiple Properties'
      }
    });
  };

  // Format date string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Configuration for payment method display
  const PAYMENT_METHOD_CONFIG: Record<string, {
    label: string;
    description: string;
    icon: (selected: boolean) => React.ReactNode;
    methodKey: 'everypay_bank' | 'everypay_card' | 'plaid';
  }> = {
    plaid: {
      label: 'Bank Account (UK)',
      description: 'Securely connect using Open Banking.',
      icon: (selected: boolean) => (
        <FontAwesome5 name="credit-card" size={16} color={selected ? "#2563eb" : "#6b7280"} />
      ),
      methodKey: 'plaid'
    },
    everypay_bank: {
      label: 'Bank Account (EU)',
      description: 'Securely connect using Open Banking.',
      icon: (selected: boolean) => (
        <FontAwesome5 name="landmark" size={16} color={selected ? "#2563eb" : "#6b7280"} />
      ),
      methodKey: 'everypay_bank'
    },
    everypay_card: {
      label: 'Debit/Credit Card',
      description: 'Pay securely using your card details.',
      icon: (selected: boolean) => (
        <FontAwesome5 name="credit-card" size={16} color={selected ? "#2563eb" : "#6b7280"} />
      ),
      methodKey: 'everypay_card'
    },
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white justify-center items-center">
        <StatusBar style="dark" />
        <ActivityIndicator size="large" color="#4ca2f5" />
        <Text className="mt-4 text-gray-500">Loading payment details...</Text>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-white justify-center items-center p-4">
        <StatusBar style="dark" />
        <Text className="text-red-600 text-lg mb-2">Error</Text>
        <Text className="text-gray-500 text-center mb-6">{error}</Text>
        <TouchableOpacity
          className="bg-secondary rounded-xl py-3 px-6"
          onPress={() => router.back()}
        >
          <Text className="text-white font-medium">Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50" edges={Platform.OS === 'ios' ? ['bottom'] : ['top', 'bottom']}>
      <StatusBar style="dark" />
      <ScrollView className="flex-1 px-4 w-full pb-10">
      {/* No custom Plaid modal needed - Plaid Link will create its own UI */}

      <View className="flex-1 max-w-[640px] w-full mx-auto">
        {/* Header */}
        <View className={`px-4 flex-row items-center ${Platform.OS === 'ios' ? 'pt-4 pb-4' : 'py-4'}`}>
          <TouchableOpacity
            onPress={() => router.push('/(dashboard)')}
            className="mr-4"
          >
            <Text className="text-2xl">←</Text>
          </TouchableOpacity>
          <Text className="text-xl font-semibold">Make a Payment</Text>
        </View>


          {/* Outstanding Invoices */}
          <View className="mb-8">
            <Text className="text-lg font-medium mb-4 mt-4">Outstanding Invoices</Text>

            {paymentAgreement && outstandingPayments.length > 0 ? (
              <View className='bg-white flex gap-4 rounded-xl p-5'>
                {outstandingPayments.map(payment => (
                  <TouchableOpacity
                    key={payment.id}
                    className={`border border-gray-200 rounded-xl p-4 ${payment.selected ? 'bg-secondary/5 border-secondary/15' : ''}`}
                    onPress={() => togglePaymentSelection(payment.id)}
                  >
                    <View className="flex-row justify-between items-center mb-2">
                      <View className="flex-row items-center">
                        <TouchableOpacity
                          onPress={() => togglePaymentSelection(payment.id)}
                          className="mr-2 w-6 h-6 rounded border border-gray-300 justify-center items-center"
                          style={payment.selected ? { backgroundColor: '#4ca2f5', borderColor: '#4ca2f5' } : {}}
                        >
                          {payment.selected && (
                            <Text style={{ color: 'white', fontWeight: 'bold' }}>✓</Text>
                          )}
                        </TouchableOpacity>
                        <Text className="font-bold text-lg ml-2">{formatCurrency(payment.remaining_amount, paymentAgreement?.currency || 'GBP')}</Text>
                      </View>
                      <View className="rounded-full px-3 py-1"
                        style={{
                          backgroundColor: payment.status === 'unpaid' ? '#FEE2E2' : '#FEF3C7',
                        }}>
                        <Text style={{
                          color: payment.status === 'unpaid' ? '#B91C1C' : '#92400E',
                          fontWeight: '500',
                          fontSize: 12
                        }}>
                          {payment.status === 'unpaid' ? 'Unpaid' : 'Partially Paid'}
                        </Text>
                      </View>
                    </View>

                    <Text className="text-gray-500 text-xs">Due on {formatDate(payment.due_date)}</Text>

                    <View className="mt-3 ">
                      <Text className="font-medium mb-2">Payment Details</Text>
                      {payment.lines.map((line, index) => (
                        <View key={index} className="flex-row justify-between mb-1">
                          <Text className="text-gray-500 flex-1 text-xs">{line.description}</Text>
                          <Text className="font-medium ml-2 text-xs">{formatCurrency(parseFloat(line.total_price), paymentAgreement?.currency || 'GBP')}</Text>
                        </View>
                      ))}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View className="items-center py-10 bg-white rounded-xl">
                <Text className="text-dark-gray text-center mb-2">No outstanding payments</Text>
                <Text className="text-gray-400 text-xs text-center">You're all caught up!</Text>
              </View>
            )}
          </View>

          {/* Payment Method */}
          {loadingPA ? (
            <Text>Loading payment methods...</Text>
          ) : (
            <View className="mb-4">
              <Text className="text-xl font-medium mb-4">Payment method</Text>
              {paymentAgreement?.operator?.payment_methods?.map((methodKey) => {
                const config = PAYMENT_METHOD_CONFIG[methodKey];
                if (!config) return null;

                const isSelected = selectedPaymentMethod === config.methodKey;
                const isDisabled = submitting || !outstandingPayments.some(p => p.selected);

                return (
                  <TouchableOpacity
                    key={config.methodKey}
                    className={`flex-row items-center p-4 pr-6 border rounded-xl mb-3 ${isDisabled ? 'opacity-40 ' : ''} ${isSelected ? 'border-secondary/25 bg-secondary/5' : 'bg-white border-gray-200'}`}
                    onPress={() => setSelectedPaymentMethod(config.methodKey)}
                    disabled={isDisabled}
                  >
                    <View className={`w-10 h-10 rounded-full ${isSelected ? 'bg-secondary/15' : 'bg-gray-100'} justify-center items-center mr-3`}>
                      {config.icon(isSelected)}
                    </View>
                    <View className="flex-1 pr-4">
                      <Text className="font-bold">{config.label}</Text>
                      <Text className="text-gray-500 mt-1 text-xs">{config.description}</Text>
                    </View>
                    <View className="w-6 h-6 rounded-full border-2 border-gray-300 justify-center items-center">
                      {isSelected && (
                        <View className="w-4 h-4 rounded-full bg-secondary" />
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
              {/* Render message if no allowed methods or none configured */}
              {(!paymentAgreement?.operator?.payment_methods || paymentAgreement.operator.payment_methods.length === 0) && (
                 <Text className="text-gray-500">No payment methods available for this agreement.</Text>
              )}
            </View>
          )}

        {/* Bottom Action */}
        <View className="p-4 bg-white rounded-xl">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-base font-medium">Total Amount:</Text>
            <Text className="text-xl font-medium">{formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP')}</Text>
          </View>

          <TouchableOpacity
            className={`rounded-xl py-4 items-center ${
              outstandingPayments.some(p => p.selected) && selectedPaymentMethod && !submitting
                ? 'bg-secondary'
                : 'bg-gray-200'
            }`}
            disabled={!outstandingPayments.some(p => p.selected) || !selectedPaymentMethod || submitting}
            onPress={handleEveryPayPayment}
          >
            <Text className="text-white font-bold">
              {submitting ? 'Processing...' : `Pay ${formatCurrency(totalAmount, paymentAgreement?.currency || 'GBP')}`}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      </ScrollView>
    </SafeAreaView>
  );
}