import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { Svg, Path } from 'react-native-svg';

export default function PaymentSuccessScreen() {

  React.useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'Payment Successful - CasaPay';
    }
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-white items-center">
      <StatusBar style="dark" />

      <View className="w-full max-w-[640px] flex-1 justify-center items-center p-4">
        <Svg width="49" height="48" viewBox="0 0 49 48" fill="none" style={{ marginBottom: 16 }}>
          <Path d="M24.7461 4C13.7061 4 4.74609 12.96 4.74609 24C4.74609 35.04 13.7061 44 24.7461 44C35.7861 44 44.7461 35.04 44.7461 24C44.7461 12.96 35.7861 4 24.7461 4ZM20.3461 34L10.3461 24L13.5861 20.76L20.3461 27.52L35.9061 12L39.1461 15.24L20.3461 34Z" fill="#059669"/>
        </Svg>

        <Text className="text-2xl font-semibold mb-2 text-center">
        Your payment has been processed successfully.
        You can now close this window.
        </Text>



        <TouchableOpacity
          className="bg-secondary rounded-xl py-[10px] px-4 items-center mb-4 w-[320px]"
          onPress={() => router.replace('/(dashboard)')}
        >
          <Text className="text-white font-medium">
            Close
          </Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={() => router.replace('/(dashboard)')}>
          <Text className="text-dark-gray underline">
            View Payment History
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
