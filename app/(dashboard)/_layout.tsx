import React, { useState, useRef } from 'react';
import { Stack, router } from 'expo-router';
import { Modal, View, Text, TouchableOpacity, Platform, Dimensions } from 'react-native';
// SafeAreaView might still be needed if other parts of this file use it,
// or it could be removed if this is the only usage.
// For now, let's comment it out or remove if not used elsewhere in this file.
// import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather as Icon } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import { Logo } from '@/components/ui/Logo';

export default function DashboardLayout() {
  const { state, logout } = useAuth();
  const user = state.user;

  // Profile dropdown state
  const [profileDropdownVisible, setProfileDropdownVisible] = useState(false);
  // Initialize with a default, will be updated on layout
  const [profileMenuPosition, setProfileMenuPosition] = useState({ top: 0, right: 0 });
  const profileButtonRef = useRef<React.ElementRef<typeof TouchableOpacity>>(null);

  const toggleProfileDropdown = () => {
    if (profileButtonRef.current) {
      profileButtonRef.current.measureInWindow((x: number, y: number, width: number, height: number) => {
        // Get actual screen dimensions
        const screenWidth = Dimensions.get('window').width;
        const dropdownWidth = 240;

        // Position dropdown below the button
        const top = y + height + 5; // 5px gap below button

        // Calculate right position to align dropdown with right edge of button
        // We want the dropdown to appear below and to the right, aligned with the button's right edge
        const right = screenWidth - (x + width);

        console.log('📍 Positioning dropdown:', {
          screenWidth,
          buttonX: x,
          buttonY: y,
          buttonWidth: width,
          buttonHeight: height,
          calculatedTop: top,
          calculatedRight: right
        });

        setProfileMenuPosition({
          top: top,
          right: Math.max(10, right) // Ensure it doesn't go off screen
        });
      });
    }
    setProfileDropdownVisible(prev => !prev);
  };

  const handleLogout = async () => {
    setProfileDropdownVisible(false);
    await logout();
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header */}
      <View
        className={`flex-row justify-between items-center px-5 pb-2 bg-white border-b border-gray-200 z-10`}
        style={{ paddingTop: Platform.OS === 'web' ? 10 : 0 }}
      >
        <Logo size="small" textClass="text-xl font-semibold text-primary" />
        <View className="flex-row items-center">
          <TouchableOpacity
            ref={profileButtonRef}
              className="p-0.5 flex-row items-center"
              onPress={toggleProfileDropdown}>
              <View className="w-8 h-8 rounded-full bg-secondary items-center justify-center mr-2">
                <Text className="text-white text-sm font-semibold">{user?.first_name?.charAt(0).toUpperCase() || 'U'}</Text>
              </View>
              <Icon name="chevron-down" size={16} color="#4b5563" />
            </TouchableOpacity>
          </View>
        </View>
      {/* Profile Dropdown Modal */}
      <Modal
        visible={profileDropdownVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setProfileDropdownVisible(false)}>
        <TouchableOpacity
          className="flex-1 bg-black/30" // Removed justify-start items-start to allow full screen click
          activeOpacity={1}
          onPress={() => setProfileDropdownVisible(false)}>
          <View
            className="absolute bg-white rounded-xl w-[240px] shadow-xl z-20" // Increased z-index
            style={{
              top: profileMenuPosition.top,
              right: profileMenuPosition.right,
            }}
            // Prevent click inside modal from closing it
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => e.stopPropagation()}
            >
            {/* User profile section */}
            <View className="px-4 py-3 border-b border-gray-200">
              <View className="flex-row items-center mb-1">
                <View className="w-10 h-10 rounded-full bg-secondary items-center justify-center mr-3">
                  <Text className="text-white text-base font-semibold">{user?.first_name?.charAt(0).toUpperCase() || 'U'}</Text>
                </View>
                <View>
                  <Text className="text-sm font-medium text-gray-800" numberOfLines={1} ellipsizeMode="tail">
                    {`${user?.first_name || ''} ${user?.last_name || ''}`.trim()}
                  </Text>
                  <Text className="text-xs text-gray-500" numberOfLines={1} ellipsizeMode="tail">
                    {user?.email || '-'}
                  </Text>
                </View>
              </View>
            </View>

            {/* Menu options */}
            <View className="py-2">
              <TouchableOpacity
                className="flex-row items-center py-2.5 px-4 hover:bg-gray-100 active:bg-gray-200"
                onPress={() => {
                  setProfileDropdownVisible(false);
                  router.push('/(dashboard)/profile'); // Ensure correct path
                }}>
                <Icon name="user" size={16} color="#4b5563" style={{ marginRight: 12 }} />
                <Text className="text-sm text-gray-700">Profile</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-row items-center py-2.5 px-4 hover:bg-red-50 active:bg-red-100"
                onPress={handleLogout}>
                <Icon name="log-out" size={16} color="#ef4444" style={{ marginRight: 12 }} />
                <Text className="text-sm text-red-600">Log out</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
      <Stack
        screenOptions={{
          headerShown: false,
        }}
       />
    </View>
  );
}
