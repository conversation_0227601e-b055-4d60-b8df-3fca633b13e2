const { config } = require('dotenv');
const path = require('path');

// Load environment variables from .env file
const env = config({ path: path.resolve(__dirname, '.env') });

// Get the app.json configuration
const appJson = require('./app.json');

// Export the configuration with environment variables
module.exports = {
  ...appJson,
  expo: {
    ...appJson.expo,
    extra: {
      ...appJson.expo.extra,
      // Add environment variables here
      PROXY_URL: process.env.PROXY_URL || env.parsed?.PROXY_URL,
    },
  },
};
