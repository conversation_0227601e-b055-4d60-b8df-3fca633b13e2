const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');
  
const config = getDefaultConfig(__dirname);

// Add platform-specific resolver that only blocks react-native-pager-view on web
const originalResolveRequest = config.resolver.resolveRequest;
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Only block react-native-pager-view on web platform
  if (platform === 'web' && moduleName.includes('react-native-pager-view')) {
    return {
      type: 'empty',
    };
  }
  
  // Use original resolver for all other cases (including native platforms)
  if (originalResolveRequest) {
    return originalResolveRequest(context, moduleName, platform);
  }
  
  return context.resolveRequest(context, moduleName, platform);
};

module.exports = withNativeWind(config, { input: './global.css' });