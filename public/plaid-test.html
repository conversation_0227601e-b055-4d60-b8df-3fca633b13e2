<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plaid Sandbox Test</title>
  <script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 20px;
    }
    .info {
      background-color: #f0f9ff;
      border: 1px solid #93c5fd;
      border-radius: 4px;
      padding: 16px;
      margin: 20px 0;
    }
    .success {
      background-color: #ecfdf5;
      border: 1px solid #6ee7b7;
      border-radius: 4px;
      padding: 16px;
      margin: 20px 0;
      display: none;
    }
    h3 {
      margin-top: 0;
    }
    pre {
      background-color: #f1f5f9;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Plaid Sandbox Test</h1>
  
  <div class="info">
    <h3>Sandbox Test Credentials</h3>
    <p>Use these credentials to test different scenarios:</p>
    <ul>
      <li><strong>Username:</strong> user_good</li>
      <li><strong>Password:</strong> pass_good</li>
      <li>For more test users, see the <a href="https://plaid.com/docs/sandbox/test-credentials/" target="_blank">Plaid documentation</a></li>
    </ul>
  </div>

  <button id="linkButton">Connect a bank account</button>
  
  <div id="successContainer" class="success">
    <h3>Success!</h3>
    <p>Bank account successfully connected.</p>
    <h4>Response Data:</h4>
    <pre id="responseData"></pre>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // For testing purposes, we'll use a hardcoded sandbox link token
      // In a real app, you would get this from your server
      const testLinkToken = 'link-sandbox-12345';
      
      document.getElementById('linkButton').addEventListener('click', function() {
        // Initialize Plaid Link
        const handler = Plaid.create({
          token: testLinkToken, // In production, get this from your server
          onSuccess: function(public_token, metadata) {
            // Display success message and metadata
            document.getElementById('successContainer').style.display = 'block';
            document.getElementById('responseData').textContent = JSON.stringify({
              public_token: public_token,
              metadata: metadata
            }, null, 2);
            
            // In a real app, you would send the public_token to your server
            console.log('Success!', public_token, metadata);
          },
          onExit: function(err, metadata) {
            if (err != null) {
              console.error('Error during Link flow:', err);
            }
            console.log('User exited Link flow:', metadata);
          },
          onEvent: function(eventName, metadata) {
            console.log('Link event:', eventName, metadata);
          },
          receivedRedirectUri: window.location.href,
        });
        
        handler.open();
      });
    });
  </script>
</body>
</html>
